export default {
	methods: {
		handleFocus() {
			var _that = this;
			_that.focus = false;
			setTimeout(() => {
				_that.focus = true;
			}, 500);
		},
		search() {
			var _that = this;
			_that.focus = false

			//#endregion
			// #ifdef APP-PLUS
			var mpaasScanModule = uni.requireNativePlugin("Mpaas-Scan-Module");
			mpaasScanModule.mpaasScan({
				// 扫码识别类型，参数可多选，qrCode、barCode，不设置，默认识别所有
				'scanType': ['qrCode', 'barCode'],
				// 是否隐藏相册，默认false不隐藏
				'hideAlbum': false,
				//ios需要设置这个参数，只支持中英文 zh-Hans、en，默认中文                       
				'language': 'en',
				//相册选择照片识别错误提示(ios)
				'failedMsg': '未识别到二维码，请重试',
				//Android支持全屏需要设置此参数
				'screenType': 'full'
			},
				async (ret) => {
					console.log('ret', ret)
					if (ret.resp_message == 'success') {
						_that.barCode = ret.resp_result;
						
						await _that.commonSearch(ret.resp_result)
					}
				})
			// #endif
			// #ifdef H5 || MP-WEIXIN || MP-QQ 
			uni.scanCode({
				scanType: ["barCode", "qrCode"],
				success: async (res) => {
					_that.barCode = res.result;

					await _that.commonSearch(res.result)
				}
			})
			// #endif
		},
		async confirmInput() {
			this.focus = false
			console.log(this.barCode, 'barCode')
			await this.commonSearch(this.barCode)
		},
		debounce(func, wait) {
			let timeout;
			return function(...args) {
				const context = this;
				if (timeout) clearTimeout(timeout);
				timeout = setTimeout(() => {
					func.apply(context, args);
				}, wait);
			};
		},
		async commonSearch(barCode) {
			let that = this
			// 如果barCode 为空，则提示请先确定客户
			if(!barCode){
				// that.$refs.jsError.showError('','请先扫描条码','error');
				return false;
			}
			if(!that.model.cusName){
				that.$refs.jsError.showError('','请先确定客户','error');
				this.barCode = ''
				this.$forceUpdate()
				if(this.vuex_config.deviceType === this.vuex_config.deviceTypePDA) {
					this.handleFocus()
				}
				
				return false;
			}
			
			if(!!that.isBack){

				let content = ''
				if(this.type === 'fa'){
					content = '当前产品正在进行发货，是否取消发货！'
				}else if(this.type === 'tui'){
					content = '当前产品正在进行退货，是否取消退货！'
				}

				uni.showModal({
					title: '温馨提示',
					content: content,
					confirmColor: '#F54E40',
					success: async (res) => {
						if (!res.confirm) {
							return false;
						}
						// 查找barcode 是否在cacheList里面
						// const bizType = that.vuex_config.bizType_Tui
						// await that.getValidate(bizType,barCode)buxun
						console.log(that.cacheList,barCode)
						
						const bizKey = this.bizKey
						this.$u.api.zgz.deleteCache({
							bizKey,
							barCode,
						}).then(res => {
							if (res.result == 'true') {
								let index = that.cacheList.findIndex(item => item.barCode === res.data.barCode)
								if(index !== -1){
									this.handleValidationTui(res.data,res.data.barCode)
									setTimeout(()=>{
										this.makeSound("cg");
										if(this.cacheList.length === 0){
											this.model.cinvCode = ''
											this.model.cinvname = ''
										}
									},500)	
								}else{
									setTimeout(()=>{
										that.makeSound("bcz");
									},500)
									that.$refs.jsError.showError('','当前码不存在','error');
								}
								
								
															
							} else {
								setTimeout(()=>{
									this.makeSound("sb");
								},500)
								this.$refs.jsError.showError('', res.message, 'error');
							}
							setTimeout(() => {
								this.barCode = '';
							}, 500);
							if(this.vuex_config.deviceType === this.vuex_config.deviceTypePDA){
								this.handleFocus()
							}
							
							
						})
						// if(index !== -1){
							

						// }else{
						// 	setTimeout(()=>{
						// 		that.makeSound("bcz");
						// 	},500)
						// 	that.$refs.jsError.showError('','当前码不存在','error');
						// }
						
						that.isBack = false
						that.$forceUpdate()
						
					}
				})
			}else{
				let index = that.cacheList.findIndex(item => item === barCode)
				if(index !== -1){
					that.$refs.jsError.showError('','请务重复扫码','error');
				}else{
					let bizType = ''
					if(this.type === 'fa'){
						bizType = that.vuex_config.bizType_Fa
					}else if(this.type === 'tui'){
						// this.model.bizType = this.vuex_config.bizType_Tui
						bizType = that.vuex_config.bizType_Tui
					}
					// const bizType = that.vuex_config.bizType_Fa
					that.getValidate(bizType,barCode)
				}
				if(this.vuex_config.deviceType === this.vuex_config.deviceTypePDA){
					this.handleFocus()
				}
			}
			setTimeout(() => {
				this.barCode = '';
			}, 500);
			
			
		},
		async getValidate(bizType,barCode){
			
			const cusCode = this.model.cusCode
			// this.model.bizKey = this.bizKey
			const bizKey = this.bizKey
			console.log('bizKey---',bizKey,this)
			await this.$u.api.zgz.validate({
				bizKey,
				barCode,
				bizType,
				cusCode
			}).then(res => {
				if (res.result == 'true') {
					console.log('-------',bizKey,barCode,bizType,cusCode,res)
					if(res.data.status == 200){
						this.model.cinvname = res.data.basInv.cinvname
						this.model.cinvCode = res.data.basInv.cinvcode
						if(!!this.isBack){
							this.handleValidationTui(res.data,barCode)
						}else{
							this.handleValidationFa(res.data,barCode)
							
						}
					}else if(res.data.status == 201){
						setTimeout(()=>{
							this.makeSound("yf");
						},500)
						this.$refs.jsError.showError('', res.data.error, 'error');
					}else if(res.data.status == 202){
						setTimeout(()=>{
							this.makeSound("sb");
						},500)
						this.$refs.jsError.showError('', res.data.error, 'error');
					}else if(res.data.status == 203){
						setTimeout(()=>{
							this.makeSound("bcz");
						},500)
						this.$refs.jsError.showError('', res.data.error, 'error');
					}					
					
				} else {
					setTimeout(()=>{
						this.makeSound("sb");
					},500)
					this.$refs.jsError.showError('', res.message, 'error');
				}
				setTimeout(() => {
					this.barCode = '';
				}, 500);
				// if(this.vuex_config.deviceType === this.vuex_config.deviceTypePDA){
				// 	this.handleFocus()
				// }
						
			})
		},
		handleValidationFa(data, barCode){
			let that = this;
			if(data.boxType === that.vuex_config.xiangPrefix){
				// 数量加1
				that.xiangNums++
				console.log(that.xiangNums,'that.xiangNums')
				
				that.xcodes += data.barCode + ','
				
				
			}else if(data.boxType === that.vuex_config.hePrefix){
				// 数量加1
				that.heNums++

				
			}else if(data.boxType === that.vuex_config.pingPrefix){
				// 数量加1
				that.pingNums++
			}else if(data.boxType === that.vuex_config.guanPrefix){
				// 数量加1
				that.guanNums++
				that.gcodes += data.barCode + ','
			}
			// 返回一个新的数组，res.list数组里面 lastNums的数组长度大于1，设置lastNum 为''，把basMold 里面的值平铺，并且lastNums的数组长度大于1，设置lastNum 为''
			that.cacheList.push({
				barCode:data.barCode,
				boxType:data.boxType,
				bizKey:that.bizKey
			})	
			that.$forceUpdate()
			setTimeout(()=>{
				that.makeSound("cg");
			},500)
			that.$u.toast('成功！');
		},
		handleValidationTui(data, barCode){
			console.log(data,barCode,'handleValidationTui')
			let that = this;
			if(data.boxType === that.vuex_config.xiangPrefix){
				// 数量加1
				that.xiangNums--
				console.log(that.xiangNums,'that.xiangNums')
				// 把箱码放在xcodes 里面,用逗号隔开
				// that.xcodes += data.barCode + ','
				//// 把对应的码从xcodes 字符串里面移出去 "xxxx,aaaa,bbb" 把 "bbb移出" 
				const xcodesArray = that.xcodes.split(',');
				const FilterXcodesArray = xcodesArray.filter(item => item !== data.barCode)

				that.xcodes = FilterXcodesArray.join(',')

			}else if(data.boxType === that.vuex_config.hePrefix){
				// 数量加1
				that.heNums--

				
			}else if(data.boxType === that.vuex_config.pingPrefix){
				// 数量加1
				that.pingNums--
			}else if(data.boxType === that.vuex_config.guanPrefix){
				// 数量加1
				that.guanNums--
				const xcodesArray = that.gcodes.split(',');
				const FilterXcodesArray = xcodesArray.filter(item => item !== data.barCode)
				that.gcodes = FilterXcodesArray.join(',')
			}
			// 返回一个新的数组，res.list数组里面 lastNums的数组长度大于1，设置lastNum 为''，把basMold 里面的值平铺，并且lastNums的数组长度大于1，设置lastNum 为''
			// that.cacheList.push(barCode)	
			// that.cacheList.splice(index, 1)
			let index = that.cacheList.findIndex(item => item.barCode === barCode)
			if(index !== -1){
				that.cacheList.splice(index, 1)
				console.log(that.cacheList,'that.cacheList')
				that.$forceUpdate()
				setTimeout(()=>{
					that.makeSound("cg");
				},500)
				that.$u.toast('成功！');
			}else{
				setTimeout(()=>{
					that.makeSound("bcz");
				},500)
				that.$refs.jsError.showError('','当前码不存在','error');
			}
			
		},

		makeSound(name) {
			console.log("=====testClick=====");
			let src = '/static/jeesite/' + name + '.mp3';
			//实例化声音  
			const Audio = uni.createInnerAudioContext();
			Audio.autoplay = true;
			Audio.src = src; //音频地址  
			Audio.play(); //执行播放  
			Audio.onError((res) => {
			});
			Audio.onPause(function () {
				console.log('end');
				Audio.destroy();
			});
		},
	}
};