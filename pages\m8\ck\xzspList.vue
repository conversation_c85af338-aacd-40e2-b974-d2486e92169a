<template>
	<view>
		<js-error mode="bottom" ref="jsError"></js-error>
		<u-sticky class="u-sticky">
			<view class="padding-sm flex light " style="background-color: #eee;" @tap="show=true">
				<view style="width: 100%"><u-search placeholder="物料名称/代码/规格型号等" v-model="keyword" bg-color="#fff"
						:show-action="false" :disabled="true" @tap="show=true" searchIconSize="26"
						:inputStyle="inputStyle"></u-search>
				</view>
			</view>
		</u-sticky>
		<!-- <view class="search">
			<u-search v-model="query.invName" :show-action="false" @search="loadData" placeholder="输入商品名称搜索" ></u-search>
		</view> -->
		<u-popup v-model="show" mode="right" length="90%">
			<!-- class="form"  -->
			<u-form class="form" style="padding: 0 10px;" :model="query" ref="uForm" label-position="left">
				
				<view style="" class="text-border">
					<view class="text text-lg text-green">基本信息</view>
					<u-form-item  label="物料代码:" prop="viewCode" label-width="230">
						<u-input placeholder="请输入" v-model="query['viewCode']" type="text" maxlength="200"></u-input>
					</u-form-item>
					<u-form-item  label="物料名称:" prop="invName" label-width="230">
						<u-input placeholder="请输入" v-model="query['invName']" type="text" maxlength="200"></u-input>
					</u-form-item>
					<u-form-item  label="规格型号:" prop="invStd" label-width="230">
						<u-input placeholder="请输入" v-model="query['invStd']" type="text" maxlength="200"></u-input>
					</u-form-item>
					<u-form-item  label="客户图号:" prop="f665" label-width="230">
						<u-input placeholder="请输入" v-model="query['f665']" type="text" maxlength="200"></u-input>
					</u-form-item>
				</view>
				
			</u-form>
			<!-- <view class="footer">
			<u-button class="btn" type="primary" @click="submit">查询</u-button>
		</view> -->
			<view class="cu-bar"></view>
			<view class=" flex cu-bar tabbar bg-white foot cu-modal-footer">
				<!-- round -->
				<button class="cu-btn  lines-red lg " @click="reset">重置</button>
				<button class="cu-btn  bg-confirm lg margin-left " @click="submit">查询</button>
			</view>
		</u-popup>
		
		<scroll-view scroll-y="true" @scrolltolower="loadMore" :refresher-enabled="true" :scrolltolower-enabled="true"
			:refresher-triggered="triggered" @refresherrefresh="refresherrefresh" @scrolltoupper="refresherrefresh"
			class="scroll-view-class" :style="{ height: computedScrollViewHeight }">
			<view class="scroll-content" ref="scrollContent" style="
          display: flex;
          flex-direction: column;
          justify-content: space-between;
        " :style="{ height: computedScrollViewHeight  }">
				<view v-for="(item,index) in list"  @click="xzsp(item)" class="cu-item shadow " style="position: relative;margin-bottom: 10px;" :key="item.id">
					<view class="cu-form-group"  style="display: flex;justify-content: space-between;">
						<view
							style=" display: inline-block;padding: 5px;background: #3E97B0;color: #fff;min-width: 40px;text-align: center;font-style: italic;font-weight: bold">
							{{ index + 1 }}
						</view>
						<view style="flex: 1;" class="margin-left-lg text-blue text-xl"> {{ item.invName|| ""  }}</view>
						<!-- <view class="text-red text-bold"> {{ item.djno|| ""  }}</view>
						<view class="">
							<dictLabel style="margin-left: 10px;" :value="item.djStatus" dict-type="xy_asn_dj_status">
							</dictLabel>
						
						</view> -->
					</view>
					<view class="cu-form-group">
						<view class="title">物料代码：</view>
						<view style="flex: 1;"> {{ item.viewCode|| ""  }}</view>
					</view>
					<view class="cu-form-group">
						<view class="title">规格型号：</view>
						<view style="flex: 1;"> {{ item.invStd|| ""  }}</view>
					</view>
					<view class="cu-form-group">
						<view class="title">客户图号：</view>
						<view style="flex: 1;"> {{ item.f665|| ""  }}</view>
					</view>
					<view class="cu-form-group">
						<view class="title">物料分类：</view>
						<view style="flex: 1;"> {{ (item.basInvCls && item.basInvCls.name) || "" }}</view>
					</view>
					
					<view class="cu-form-group">
						<view class="title">计量单位：</view>
						<view style="flex: 1;">{{ item.funitname || ""  }}</view>
					</view>
					<!-- <view
					  class="cu-form-group"
					  style="color: #3e97b0"
					>
						<view class="">
						</view>
						<view @click="toForm(item)">
							查看详情<u-icon name="arrow-right"></u-icon>
						</view>
					</view> -->
					
				</view>

				<view v-if="list.length" class="loadmore" style="justify-self: flex-end">
					<u-loadmore :status="loadStatus"></u-loadmore>
				</view>
				<u-empty v-if="!list.length" style="height: 80vh;"></u-empty>
			</view>
		</scroll-view>

		
	</view>
</template>
<script>
	import {
		hasPermission
	} from '@/common/fire.js'
	import dictLabel from "@/components/dictLabel.vue";
	export default {
		components: {
			dictLabel,
		},
		data() {
			return {
				companySelectList:[],
				show: false,
				smshow: false,
				focus: true,
				barCode: "",
				remarks: '',
				czData: {},
				show2: false,
				type: 'text',
				startTime: false, //控制日期显示
				endTime: false, //控制日期显示
				show: false,
				inputStyle: {
					fontSize: '34rpx'
				},
				filterIcon: "/static/image/filter.png",
				keyword: "",
				background: {
					backgroundColor: '#3E97B0',
				},
				list: [],
				itemContent: {},
				query: {
					pageNo: 1,
					pageSize: 5,
					// companyCode:this.vuex_company.companyCode || '',
					// 'company.companyName':this.vuex_company.companyName || '',
					// company:{
					// 	companyName:this.vuex_company.companyName || '',
					// }
					// orderBy: "a.create_date desc",
				},
				loadStatus: "loadmore",
				triggered: false,
				// flag: hasPermission('app:proj:weekly:pmWeekly:edit'),
				flag: false,
				scrollViewHeight: 0,
				headerHeight: 0,
				projStatus: this.$store.state.auth.projStatus,
			};
		},
		onShow() {
			// this.query.companyCode = this.vuex_company.companyCode || '';
			// this.query['company.companyName ']= this.vuex_company.companyName || '';
			// this.query.pageNo = 1;
			// this.loadData();
			
			// this.$u.api.ktnw.companyTreeData().then(res => {
			// 	this.companySelectList = res;
			// });
		},
		onLoad(e) {
			// this.query.companyCode = e.companyCode || ''
			this.query.pageNo = 1;
			this.loadData();
		},
		mounted() {
			// this.$refs.xmInfo.$on('child-mounted-done', () => {
			//   this.calculateScrollViewHeight();
			// });
			this.calculateScrollViewHeight();
		},
		computed: {
			computedScrollViewHeight() {
				return this.scrollViewHeight === 'auto' ? 'auto' : `${this.scrollViewHeight}px`;
			},
		},
		methods: {
			xzsp(item){
				uni.$emit('xzsp',item);
				uni.navigateBack({
					delta: 1,
				});
			},
			toForm(item){
				const that = this
				uni.navigateTo({
					url: '/pages/xy/asnH/form?djno=' + item.djno,
				})
			},
			submit() {
				setTimeout(() => {
					this.query.pageNo = 1;
					this.loadData();
					this.show = false
				}, 100);
			},
			
			// this.$forceUpdate();
			reset() {
				this.list = [];
				this.query = {
					pageNo: 1,
					pageSize: 20,
				};
				this.loadData();
				this.show = false
			},
			
			startConfirm(e) {
				this.query.arrDate_gte = e.year + '-' + e.month + '-' + e.day;
			},
			endConfirm(e) {
				this.query.arrDate_lte = e.year + '-' + e.month + '-' + e.day;
			},
			customBack() {
				// 跳转到工作台页面
				// uni.navigateTo({
				//   url: "/pages/zfgs/index/index/index?item=" + JSON.stringify(this.itemContent),
				// });
				uni.navigateBack({
					delta: 1,
				});
			},
			async calculateScrollViewHeight() {
				try {
					// 等待头部高度计算完成
					// await new Promise((resolve) => {
					//   this.$nextTick(() => {
					//     this.headerHeight = this.$refs.xmInfo.$refs['u-sticky'].height + this.$refs.xmInfo.$refs['u-sticky'].h5NavHeight + this.$refs.navbar.navbarHeight;
					//     resolve();
					//   });
					// });
					this.headerHeight = 52
					// 计算tabBar高度
					if (this.flag) {
						this.tabBarHeight = 60; // 假设tabbar高度为50px，实际应根据实际情况获取
					} else {
						this.tabBarHeight = 0;
					}
					// 获取屏幕高度
					const systemInfo = uni.getSystemInfoSync();
					const screenHeight = systemInfo.windowHeight;
					// 计算scroll-view高度
					this.scrollViewHeight = screenHeight - (this.headerHeight + this.tabBarHeight);
				} catch (error) {
					console.error('Error while calculating ScrollView height:', error);
				}
			},

			loadMore() {
				this.loadStatus = "loading";
				setTimeout(() => {
					this.query.pageNo += 1;
					this.loadData("add");
				}, 100);
			},
			loadData(type) {
				// 循环10次重复赋值  needShListData
				this.$u.api.m8.basInvListData(this.query).then((res) => {
					if (res.list.length >= 0) {
						// this.xmList = res.list
						if (res.list.length < 20 || res.list.length == 0) {
							this.loadStatus = "nomore";
						}
						var data = res.list;
						if (type == "add") {
							for (var i = 0; i < data.length; i++) {
								this.list.push(data[i]);
							}
						} else {
							this.list = data;
						}
					}
				});
			},
			async refresherrefresh() {
				this.triggered = true;
				this.query.pageNo = 1;
				await this.loadData();
				const scrollTop = 0
				uni.pageScrollTo({
					scrollTop,
					duration: 0,
				});
				setTimeout(() => {
					this.triggered = false;
				}, 500);
			},
		},
		// beforeDestroy() {
		// 	// 清理事件监听器
		// 	try {
		// 		this.$refs.xmInfo.$off('child-mounted-done');
		// 	} catch (e) {
		// 		console.log('Error while unbinding event:', e);
		// 	}
		// }
	};
</script>
<style lang="scss" scoped>
	.text-border{
		border:2px solid  rgb(170, 170, 170); 
		border-radius: 5px;
		position: relative;
		padding-top: 20rpx;
		margin-top: 40rpx;
		padding-left: 20rpx;
		.text{
			position: absolute;
			background-color: #fff;
			padding: 10rpx 10rpx;
			top: -25rpx;
			right: 30rpx;
		}
	}
	.custom-style{
		// background-color: rgba(25, 190, 107, 0.7);
		
	}	
	.text-xxl{
		font-size:60rpx;
	}
	.cu-bar {
		min-height: 60px;
	}
	
	.cu-btn {
		font-size: 16px;
		height: 40px;
	}
	
	.foot {
		background: #fff;
	}
	
	.cu-item {
		flex-wrap: wrap;
		padding: 8px 15px !important;
	}
	
	.title {
		width: 200rpx;
	}
	
	.cu-modal-slot {
		margin: 42rpx 32rpx 20rpx 32rpx;
		height: 100vh;
		position: relative;
	}
	
	.cu-modal-header {
		font-size: 38rpx;
		color: #3d3d3d;
		line-height: 40rpx;
		text-align: left;
		font-weight: 500;
	}
	
	.button {
		font-size: 32rpx;
		color: #666666;
		line-height: 40rpx;
		padding: 12rpx 40rpx;
		margin-bottom: 20rpx;
		background: #e2e2e2;
		border-radius: 180rpx;
	}

	.button:hover {
		background: #3e97b0;
		color: #ffffff;
	}

	.cu-modal-search-title {
		margin: 32rpx 0rpx 20rpx 0rpx;
		text-align: left;
		font-size: 32rpx;
		color: #999999;
		line-height: 40rpx;
	}

	.cu-dialog {
		background: #ffffff;
	}

	.cu-modal-footer {
		padding: 32rpx 32rpx;
		width: 100%;

		.cu-btn {
			width: 50%;
		}
	}

	.bg-confirm {
		background: #3e97b0;
		color: #ffffff;
	}
</style>