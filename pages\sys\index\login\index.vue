<template>
	<view class="wrap">
		<view v-if="!viewShow">
		<!-- <js-lang title="login.title" :showBtn="true"></js-lang> -->
			<view class="logo"><image src="/static/jeesite/logo200.png" mode="aspectFit"></image></view>
			<!-- 注释部分为小程序登录 -->
			<!-- <view v-if="isBind || xcxMessage">
				<view v-if="isBind">
					<view class="list">
						<view class="list-call">
							<u-icon class="u-icon" size="40" name="phone"></u-icon>
							<input class="u-input" disabled type="text" v-model="username" maxlength="32" placeholder="请先获取手机号" />
						</view>
					</view>
					<view style="padding: 20px;">
						<button style="background-color: #3E97B0;color: #fff;border: none;" open-type="getPhoneNumber"
							@getphonenumber="getPhoneNumber" plain="true">获取手机号</button>
					</view>
				</view>
				<view v-if="xcxMessage"
					style="margin-top: 20px;font-size: 20px;color: red;display: flex;justify-content: center;align-items:center;">
					<u-icon name="error-circle" size="50"></u-icon>
					<text style="margin-left: 5px;">
						{{xcxMessage}}
					</text>
				</view>
				<view v-if="!xcxMessage" style="text-align: center;margin-top: 20px;font-size: 20px;">
					自动登录中...
				</view>
			</view>
			<view v-else>
				
				
			</view> -->
			<view class="list">
				<view class="list-call">
					<u-icon class="u-icon" size="40" name="account"></u-icon>
					<input class="u-input"  type="text" v-model="username" maxlength="32" :placeholder="$t('login.placeholderAccount')" />
				</view>
				<view class="list-call">
					<u-icon class="u-icon" size="40" name="lock"></u-icon>
					<input class="u-input" type="text" v-model="password" maxlength="32" :placeholder="$t('login.placeholderPassword')" :password="!showPassword" />
					<image class="u-icon-right" :src="'/static/jeesite/login/eye_' + (showPassword ? 'open' : 'close') + '.png'" @click="showPass()"></image>
				</view>
				<view class="list-call" v-if="isValidCodeLogin">
					<u-icon class="u-icon" size="40" name="coupon"></u-icon>
					<input class="u-input" type="text" v-model="validCode" maxlength="4" placeholder="验证码" />
					<u-image class="img-valid-code" width="300rpx" height="90rpx" :src="imgValidCodeSrc" @click="refreshImgValidCode()"></u-image>
				</view>
				
			</view>
			<view class="button" hover-class="button-hover" @click="submit()"><text>{{$t('login.loginButton')}}</text></view>
	
		</view>
		
		
		<view v-else style="height: 80vh;display: flex;justify-content: center;align-items: center;">
			
			<view class="">
				<!-- <view class="logo">
					<image src="/static/jeesite/logo200.png"></image>
				</view> -->
				<view class="logo"><image src="/static/jeesite/logo200.png" mode="aspectFit"></image></view>
					
				<view v-if="viewShow=='加载'" style="text-align: center;margin-top: 20px;font-size: 20px;">
					加载中！！！
				</view>
				
				<view v-else style="margin-top: 20px;font-size: 20px;color: red;display: flex;justify-content: center;align-items:center;">
					<u-icon name="error-circle" size="50"></u-icon>
					<text style="margin-left: 5px;">
						{{viewShow}}
					</text>
					
				</view>
				
			</view>
		</view>
		
		
		<u-popup v-model="show" mode="bottom" height="260px" :mask-close-able='false'>
			<view style="width: 100%;height: 260px;padding: 10px 50px;background-color: #eee;font-size: 12px;">
				<view class="">授权申请</view>
				<view style="margin: 10px 0;">获取你的昵称、头像</view>
				<view
					style="border-top:1px solid #bcbcbc;border-bottom:1px solid #bcbcbc;padding: 10px 0;display: flex;margin-bottom: 10px;">
					<!-- <view style="width: 50px;height: 50px; margin-right: 10px;">
						<open-data type="userAvatarUrl"></open-data>
					</view>
					<view class="">
						<open-data type="userNickName"></open-data>
						<view style="color: #aaa;">
							微信个人信息
						</view>
					</view> -->
					<view style="width: 70px;height: 70px; margin-right: 10px;">
						<button style="width: 70px;height: 70px;display: flex;padding: 0;" open-type="chooseAvatar"
							@chooseavatar="onChooseAvatar">
							<image style="width: 70px; height: 70px;" :src="avatarUrl"></image>
						</button>
					</view>
					<input v-model="nickname1" @change="getNickname" type="nickname" class="weui-input"
						placeholder="请输入昵称" />
				</view>
				<view style="display: flex;justify-content: center;">
					<view @click="reject"
						style="width: 80px;height: 30px;line-height:30px;text-align: center;margin-right: 20px;background-color: #fff;color: #00aa00;">
						拒绝</view>
					<view v-if="nickname1 && avatarUrl" @click="allow"
						style="width: 80px;height: 30px;line-height:30px;text-align: center;background-color: #00aa00;color: #fff;">
						允许</view>
					<view v-else
						style="width: 80px;height: 30px;line-height:30px;text-align: center;background-color: #aaa;color: #eee;">
						允许</view>
				</view>
			</view>
		
		
		</u-popup>
	</view>
</template>
<script>
/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 */
import base64 from '@/common/base64.js';
import config from '@/common/config.js';
import menuMatcher from '@/utils/menuMatcher.js';
import logoutHelper from '@/utils/logoutHelper.js';
import { mapMutations, mapState } from "vuex";
export default {
	data() {
		return {
			viewShow: '',
			xcxMessage: '',
			isBind: false,
			logincode:'',
			nickname1: '',
			avatarUrl: '',
			show: false,
			username: '',
			password: '',
			// username: 'system',
			// password: 'win2003..',
			showPassword: false,
			remember: true,
			isValidCodeLogin: false,
			validCode: '',
			imgValidCodeSrc: null,
			baseUrlList: config.baseUrlList,
			baseUrlValue: '',
			tabBarList: [
			
				{
					"pagePath": "/pages/sys/searchList",
					"iconPath": "/static/image/zfgs/xm.png",
					"selectedIconPath": "/static/image/zfgs/xm_select.png",
					"text": "查询",
					"index":0
				},
				{
					"pagePath": "/pages/sys/list",
					"iconPath": "/static/image/zfgs/ck.png",
					"selectedIconPath": "/static/image/zfgs/ck_select.png",
					"text": "仓库",
					"index":1
				},
				{
					// "pagePath": "/pages/m8/machine/list",
					"pagePath":"/pages/sys/jcList",
					"iconPath": "/static/image/zfgs/jc.png",
					"selectedIconPath": "/static/image/zfgs/jc_select.png",
					"text": "机床",
					"index":2
				},
				{
					"pagePath": "/pages/sys/index/user/index",
					"iconPath": "/static/image/zfgs/my.png",
					"selectedIconPath": "/static/image/zfgs/my_select.png",
					"text": "我的",
					"index":3
				}
			],
			
		};
	},
	onLoad() {
		
		let _this = this;
		//取出缓存中的账号、密码
		let res1 = wx.getSystemInfoSync()

		if (res1.environment === 'wxwork') {
			this.viewShow = '加载'
			uni.qy.login({
				suiteid: '1000043', //非必填，第三方应用的suiteid，自建应用不填。若第三方小程序绑定多个第三方应用时，建议填上该字段
				success: function(res) {
						_this.$u.api.weixin.wechatCallback({
						code: res.code
					}).then(async (resp) => {
						console.log(resp, 'wechatCallback======');
						if (resp.result == 'true') {
							_this.$u.api.authInfo().then((res) => {
								_this.setAuditList(res.stringPermissions)
							})
							_this.$u.api.menuTree().then((res) => {
								_this.setMenuList(res)
							})
							await this.getmenu()
							setTimeout(() => {
								_this.$u.vuex('vuex_user', {
									..._this.vuex_user,
									environment: 'wxwork'
								});
								uni.reLaunch({
									// url: _this.vuex_tabBarList[0].pagePath
									// url:'/pages/sys/list',
									url: '/pages/tabbar/index',
								});
								
							}, 500);
						} else {
							// _this.$u.toast(resp.message || '未连接服务器');
							_this.viewShow = resp.message || '未连接服务器'
						}
					})
				}
			});
		}else{
			
			const HBusername = uni.getStorageSync('HBusername');
			const HBpassword = uni.getStorageSync('HBpassword');
			console.log("缓存的账号:", HBusername)
			console.log("缓存的密码:", HBpassword)
			if (HBusername && HBpassword) {
				this.username = HBusername;
				this.password = HBpassword;
			}
			this.$u.api.index({loginCheck: true}).then(async res => {
				if (typeof res === 'object' && res.result !== 'login' && res.result !== 'false'){
					
					this.$u.api.authInfo().then((res) => {
						this.setAuditList(res.stringPermissions)
					})
					this.$u.api.menuTree().then((res) =>{
						this.setMenuList(res)
					})
					
					await this.getmenu()
					setTimeout(()=>{
						uni.reLaunch({
							// url: this.vuex_tabBarList[0].pagePath
							// url: '/pages/sys/list',
							url: '/pages/tabbar/index',
						});
					},500)
					
				}
			});
			
		}
		
		
		
		// this.baseUrlList.forEach(item => {
		// 	if (item.baseUrl == this.vuex_baseUrl){
		// 		this.baseUrlValue = item.value;
		// 		return;
		// 	}
		// });
	},
	computed: {
		...mapState(['vuex_tabBarList'])
	},
	methods: {
		...mapMutations("auth", ["setAuditList"]),
		...mapMutations("auth", ["setMenuList"]),
		async getPhoneNumber(e) {
			console.log(e);
			let that = this
			uni.login({
				provider: 'weixin',
				success: function(loginRes) {
					that.$u.api.zfgs.weixinMaCallback({
						code: loginRes.code,
						encryptedData: e.detail.encryptedData,
						iv: e.detail.iv,
						isBind: '1'
						// userCode: _this.vuex_user.userCode,
					}).then(res => {
						if (res.result == 'true') {
							that.$u.api.authInfo().then((res1) => {
								that.setAuditList(res1.stringPermissions)
							})
							that.$u.api.menuTree().then((res2) => {
								that.setMenuList(res2)
							})
							uni.reLaunch({
								url: '/pages/zfgs/index/notice/index'
							});
						} else {
							if (res.message.includes('[noBind]')) {
								that.isBind = true
								that.xcxMessage = '请获取手机号绑定账号'
							} else {
								that.xcxMessage = res.message
							}
						}
					});
				}
			});
		
		},
		async getmenu() {
			console.log('开始获取菜单数据...');

			try {
				// 方法1: 优先使用 findMenuList API 获取动态数据
				await this.loadTabBarFromAPI();

				// 方法2: 同时加载工序执行菜单并缓存
				await this.loadProcessMenuFromAPI();

				// 方法3: 同时加载仓库菜单并缓存
				await this.loadWarehouseMenuFromAPI();

				// 方法4: 同时加载车间菜单并缓存
				await this.loadWorkshopMenuFromAPI();

				// 方法5: 同时加载机床菜单并缓存
				await this.loadMachineMenuFromAPI();

			} catch (error) {
				console.error('获取菜单数据失败:', error);
				// API失败时设置空菜单，不显示TabBar
				console.warn('⚠️ 菜单数据获取失败，设置空菜单列表');
				this.$store.commit('modifyTabBarList', []);
			}
		},

		/**
		 * 从 findMenuList API 加载 TabBar 数据（优化版）
		 */
		async loadTabBarFromAPI() {
			console.log('尝试从 findMenuList API 获取 TabBar 数据...');

			try {
				const apiMenuList = await this.$u.api.m8.findMenuList({
					// 可以根据需要添加查询参数
					parentFuncFlag: 'menu:tabbar',
					clientFlag: this.vuex_config.menuConfig.CLIENT_APP,
				});

				if (apiMenuList && apiMenuList.length > 0) {
					console.log('✓ API 返回菜单数据:', apiMenuList.length, '项');

					// 使用菜单匹配器进行匹配
					const matchResult = menuMatcher.matchTabBarMenus(apiMenuList);

					// 保存匹配结果到本地存储
					menuMatcher.saveToLocalStorage(matchResult.allMenus, null);

					// 设置可见菜单到 Vuex
					this.$store.commit('modifyTabBarList', matchResult.visibleMenus);

					console.log('✓ TabBar 数据匹配完成，可见菜单:', matchResult.visibleMenus.length, '项');
					console.log('可见菜单列表:', matchResult.visibleMenus.map(item => item.text));

					return true;
				} else {
					console.warn('⚠️ API 未返回菜单数据，设置空菜单列表');
					// 直接设置空菜单，不显示任何TabBar菜单
					this.$store.commit('modifyTabBarList', []);
					return false;
				}

			} catch (error) {
				console.error('调用 findMenuList API 失败:', error);
				// API 失败时直接设置空菜单，不显示任何TabBar菜单
				console.warn('⚠️ API 调用失败，设置空菜单列表');
				this.$store.commit('modifyTabBarList', []);
				return false;
			}
		},

		/**
		 * 从本地存储加载 TabBar 数据
		 */
		async loadTabBarFromLocalStorage() {
			console.log('尝试从本地存储加载 TabBar 数据...');

			try {
				const localData = menuMatcher.loadFromLocalStorage();

				if (localData.tabBarMenus && localData.tabBarMenus.length > 0) {
					// 过滤出 isShow 为 true 的菜单
					const visibleMenus = localData.tabBarMenus.filter(item => item.isShow);

					if (visibleMenus.length > 0) {
						this.$store.commit('modifyTabBarList', visibleMenus);
						console.log('✓ 从本地存储加载 TabBar 数据成功:', visibleMenus.length, '项');
						return true;
					}
				}

				console.warn('⚠️ 本地存储无有效数据，使用默认配置');
				this.setDefaultTabBar();
				return false;

			} catch (error) {
				console.error('从本地存储加载 TabBar 数据失败:', error);
				this.setDefaultTabBar();
				return false;
			}
		},

		/**
		 * 从 findMenuList API 加载 工序执行 菜单（优化版）
		 */
		async loadProcessMenuFromAPI() {
			console.log('开始从 findMenuList API 加载工序执行菜单...');

			try {
				const processMenuList = await this.$u.api.m8.findMenuList({
					// 查询工序执行相关菜单
					parentFuncFlag: 'menu:m8:gongxvzhixin',
					clientFlag: this.vuex_config.menuConfig.CLIENT_APP,
				});

				if (processMenuList && processMenuList.length > 0) {
					console.log('✓ 工序执行菜单数据获取成功:', processMenuList.length, '项');

					// 使用菜单匹配器进行匹配
					const matchedProcessMenus = menuMatcher.matchProcessMenus(processMenuList);

					// 保存匹配结果到本地存储
					menuMatcher.saveToLocalStorage(null, matchedProcessMenus);

					// 同时存入 Vuex
					this.$store.commit('setProcessMenuList', matchedProcessMenus);

					console.log('✓ 工序执行菜单匹配完成并已缓存');
					return true;
				} else {
					console.warn('⚠️ 未获取到工序执行菜单数据，尝试使用本地存储');
					return this.loadProcessMenuFromLocalStorage();
				}

			} catch (error) {
				console.error('❌ 加载工序执行菜单失败:', error);
				// API 失败时尝试使用本地存储
				return this.loadProcessMenuFromLocalStorage();
			}
		},

		/**
		 * 从本地存储加载工序执行菜单
		 */
		async loadProcessMenuFromLocalStorage() {
			console.log('尝试从本地存储加载工序执行菜单...');

			try {
				const localData = menuMatcher.loadFromLocalStorage();

				if (localData.processMenus && localData.processMenus.length > 0) {
					this.$store.commit('setProcessMenuList', localData.processMenus);
					console.log('✓ 从本地存储加载工序执行菜单成功');
					return true;
				}

				console.warn('⚠️ 本地存储无工序执行菜单数据，使用默认配置');
				const defaultProcessMenus = menuMatcher.getDefaultProcessConfig();
				this.$store.commit('setProcessMenuList', defaultProcessMenus);
				return false;

			} catch (error) {
				console.error('从本地存储加载工序执行菜单失败:', error);
				return false;
			}
		},

		/**
		 * 从 findMenuList API 加载仓库菜单（优化版）
		 */
		async loadWarehouseMenuFromAPI() {
			console.log('开始从 findMenuList API 加载仓库菜单...');

			try {
				const warehouseMenuList = await this.$u.api.m8.findMenuList({
					// 查询仓库相关菜单
					parentFuncFlag: 'menu:m8:cangku',
					clientFlag: this.vuex_config.menuConfig.CLIENT_APP,
				});

				console.log('API 返回的原始仓库菜单数据:', warehouseMenuList);
				console.log('API 返回数据类型:', typeof warehouseMenuList);
				console.log('API 返回数据是否为数组:', Array.isArray(warehouseMenuList));

				if (warehouseMenuList && warehouseMenuList.length > 0) {
					console.log('✓ 仓库菜单数据获取成功:', warehouseMenuList.length, '项');

					// 详细输出每个 API 菜单项
					warehouseMenuList.forEach((item, index) => {
						console.log(`API 菜单项 ${index + 1}:`, {
							menuName: item.menuName,
							extendS2: item.extend?.extendS2,
							完整数据: item
						});
					});

					// 使用菜单匹配器进行匹配
					const matchedWarehouseMenus = menuMatcher.matchWarehouseMenus(warehouseMenuList);

					// 保存匹配结果到本地存储（需要获取现有的 TabBar 和工序菜单）
					const localData = menuMatcher.loadFromLocalStorage();
					menuMatcher.saveToLocalStorage(localData.tabBarMenus, localData.processMenus, matchedWarehouseMenus);

					// 同时存入 Vuex
					this.$store.commit('setWarehouseMenuList', matchedWarehouseMenus);

					console.log('✓ 仓库菜单匹配完成并已缓存');
					return true;
				} else {
					console.warn('⚠️ 未获取到仓库菜单数据，尝试使用本地存储');
					console.log('API 返回数据为空或无效:', warehouseMenuList);
					return this.loadWarehouseMenuFromLocalStorage();
				}

			} catch (error) {
				console.error('❌ 加载仓库菜单失败:', error);
				// API 失败时尝试使用本地存储
				return this.loadWarehouseMenuFromLocalStorage();
			}
		},

		/**
		 * 从本地存储加载仓库菜单
		 */
		async loadWarehouseMenuFromLocalStorage() {
			console.log('尝试从本地存储加载仓库菜单...');

			try {
				const localData = menuMatcher.loadFromLocalStorage();

				if (localData.warehouseMenus && localData.warehouseMenus.length > 0) {
					this.$store.commit('setWarehouseMenuList', localData.warehouseMenus);
					console.log('✓ 从本地存储加载仓库菜单成功');
					return true;
				}

				console.warn('⚠️ 本地存储无仓库菜单数据，使用默认配置');
				const defaultWarehouseMenus = menuMatcher.getDefaultWarehouseConfig();
				this.$store.commit('setWarehouseMenuList', defaultWarehouseMenus);
				return false;

			} catch (error) {
				console.error('从本地存储加载仓库菜单失败:', error);
				return false;
			}
		},

		/**
		 * 从 findMenuList API 加载车间菜单（优化版）
		 */
		async loadWorkshopMenuFromAPI() {
			console.log('开始从 findMenuList API 加载车间菜单...');

			try {
				const workshopMenuList = await this.$u.api.m8.findMenuList({
					// 查询车间相关菜单
					parentFuncFlag: 'menu:m8:chejian',
					clientFlag: this.vuex_config.menuConfig.CLIENT_APP,
				});

				console.log('API 返回的原始车间菜单数据:', workshopMenuList);
				console.log('API 返回数据类型:', typeof workshopMenuList);
				console.log('API 返回数据是否为数组:', Array.isArray(workshopMenuList));

				if (workshopMenuList && workshopMenuList.length > 0) {
					console.log('✓ 车间菜单数据获取成功:', workshopMenuList.length, '项');

					// 详细输出每个 API 菜单项
					workshopMenuList.forEach((item, index) => {
						console.log(`API 车间菜单项 ${index + 1}:`, {
							menuName: item.menuName,
							extendS2: item.extend?.extendS2,
							完整数据: item
						});
					});

					// 使用菜单匹配器进行匹配
					const matchedWorkshopMenus = menuMatcher.matchWorkshopMenus(workshopMenuList);

					// 保存匹配结果到本地存储（需要获取现有的其他菜单）
					const localData = menuMatcher.loadFromLocalStorage();
					menuMatcher.saveToLocalStorage(localData.tabBarMenus, localData.processMenus, localData.warehouseMenus, matchedWorkshopMenus);

					// 同时存入 Vuex
					this.$store.commit('setWorkshopMenuList', matchedWorkshopMenus);

					console.log('✓ 车间菜单匹配完成并已缓存');
					return true;
				} else {
					console.warn('⚠️ 未获取到车间菜单数据，尝试使用本地存储');
					console.log('API 返回数据为空或无效:', workshopMenuList);
					return this.loadWorkshopMenuFromLocalStorage();
				}

			} catch (error) {
				console.error('❌ 加载车间菜单失败:', error);
				// API 失败时尝试使用本地存储
				return this.loadWorkshopMenuFromLocalStorage();
			}
		},

		/**
		 * 从本地存储加载车间菜单
		 */
		async loadWorkshopMenuFromLocalStorage() {
			console.log('尝试从本地存储加载车间菜单...');

			try {
				const localData = menuMatcher.loadFromLocalStorage();

				if (localData.workshopMenus && localData.workshopMenus.length > 0) {
					this.$store.commit('setWorkshopMenuList', localData.workshopMenus);
					console.log('✓ 从本地存储加载车间菜单成功');
					return true;
				}

				console.warn('⚠️ 本地存储无车间菜单数据，使用默认配置');
				const defaultWorkshopMenus = menuMatcher.getDefaultWorkshopConfig();
				this.$store.commit('setWorkshopMenuList', defaultWorkshopMenus);
				return false;

			} catch (error) {
				console.error('从本地存储加载车间菜单失败:', error);
				return false;
			}
		},

		/**
		 * 设置默认 TabBar 配置（从 menu.json 获取）
		 */
		setDefaultTabBar() {
			console.log('使用默认 TabBar 配置（从 menu.json 获取 isShow=true 的菜单）');

			try {
				// 从 menu.json 中获取 isShow 为 true 的菜单项
				const originalMenus = menuMatcher.localMenuList || [];
				const visibleMenus = originalMenus.filter(item => item.isShow === true);

				console.log('menu.json 中 isShow=true 的菜单数量:', visibleMenus.length);
				visibleMenus.forEach(item => {
					console.log(`  - ${item.text} (${item.extend?.extendS2})`);
				});

				if (visibleMenus.length > 0) {
					// 转换为 API 格式
					const defaultTabBarList = visibleMenus.map(item => ({
						menuName: item.text,
						menuUrl: item.pagePath.startsWith('/') ? item.pagePath : `/${item.pagePath}`,
						extend: item.extend
					}));

					// 设置到 Vuex
					this.$store.commit('modifyTabBarList', defaultTabBarList);
					console.log('✓ 从 menu.json 设置默认 TabBar 数据成功:', defaultTabBarList.length, '项');
				} else {
					console.warn('⚠️ menu.json 中没有 isShow=true 的菜单项，设置空数组');
					this.$store.commit('modifyTabBarList', []);
				}
			} catch (error) {
				console.error('从 menu.json 获取默认 TabBar 配置失败:', error);
				// 备用方案：设置空数组
				this.$store.commit('modifyTabBarList', []);
			}
		},

		/**
		 * 从 findMenuList API 加载机床菜单（优化版）
		 */
		async loadMachineMenuFromAPI() {
			console.log('开始从 findMenuList API 加载机床菜单...');

			try {
				const machineMenuList = await this.$u.api.m8.findMenuList({
					// 查询机床相关菜单
					parentFuncFlag: 'menu:m8:jichuang',
					clientFlag: this.vuex_config.menuConfig.CLIENT_APP,
				});

				console.log('API 返回的原始机床菜单数据:', machineMenuList);
				console.log('API 返回数据类型:', typeof machineMenuList);
				console.log('API 返回数据是否为数组:', Array.isArray(machineMenuList));

				if (machineMenuList && machineMenuList.length > 0) {
					console.log('✓ 机床菜单数据获取成功:', machineMenuList.length, '项');

					// 详细输出每个 API 菜单项
					machineMenuList.forEach((item, index) => {
						console.log(`API 机床菜单项 ${index + 1}:`, {
							menuName: item.menuName,
							extendS2: item.extend?.extendS2,
							完整数据: item
						});
					});

					// 使用菜单匹配器进行匹配
					const matchedMachineMenus = menuMatcher.matchMachineMenus(machineMenuList);

					// 保存匹配结果到本地存储（需要获取现有的其他菜单）
					const localData = menuMatcher.loadFromLocalStorage();
					menuMatcher.saveToLocalStorage(localData.tabBarMenus, localData.processMenus, localData.warehouseMenus, localData.workshopMenus, matchedMachineMenus);

					// 同时存入 Vuex
					this.$store.commit('setMachineMenuList', matchedMachineMenus);

					console.log('✓ 机床菜单匹配完成并已缓存');
					return true;
				} else {
					console.warn('⚠️ 未获取到机床菜单数据，尝试使用本地存储');
					console.log('API 返回数据为空或无效:', machineMenuList);
					return this.loadMachineMenuFromLocalStorage();
				}

			} catch (error) {
				console.error('❌ 加载机床菜单失败:', error);
				// API 失败时尝试使用本地存储
				return this.loadMachineMenuFromLocalStorage();
			}
		},

		/**
		 * 从本地存储加载机床菜单
		 */
		async loadMachineMenuFromLocalStorage() {
			console.log('尝试从本地存储加载机床菜单...');

			try {
				const localData = menuMatcher.loadFromLocalStorage();

				if (localData.machineMenus && localData.machineMenus.length > 0) {
					this.$store.commit('setMachineMenuList', localData.machineMenus);
					console.log('✓ 从本地存储加载机床菜单成功');
					return true;
				}

				console.warn('⚠️ 本地存储无机床菜单数据，使用默认配置');
				const defaultMachineMenus = menuMatcher.getDefaultMachineConfig();
				this.$store.commit('setMachineMenuList', defaultMachineMenus);
				return false;

			} catch (error) {
				console.error('从本地存储加载机床菜单失败:', error);
				return false;
			}
		},

		getNickname(e) {
			this.nickname1 = e.detail.value
			console.log(e, 'getNickname===');
		},
		onChooseAvatar(e) {
			console.log(e, '===');
			this.avatarUrl = e.detail.avatarUrl
		},
		showPass() {
			this.showPassword = !this.showPassword;
		},
		async getPhoneNumber(e) {
			console.log(e);
		      try {
		        const encryptedData = e.detail.encryptedData;
		        const iv = e.detail.iv;
		
		        // 将加密后的数据发送到后端
		        // this.sendToServer(encryptedData, iv);
		      } catch (error) {
		        console.error('获取手机号失败', error);
		      }
		},
		refreshImgValidCode(e) {
			if (this.vuex_token == '') {
				this.$u.api.index().then(res => {
					this.imgValidCodeSrc = this.vuex_config.baseUrl + '/validCode?__sid='
						+ res.sessionid + '&t=' + new Date().getTime();
				});
			} else {
				this.imgValidCodeSrc = this.vuex_config.baseUrl + '/validCode?__sid='
						+ this.vuex_token + '&t=' + new Date().getTime();
			}
			this.validCode = '';
		},
		reject() {
			this.show = false;
			// 拒绝授权时清除缓存但不跳转
			logoutHelper.clearCacheOnly();
		},
		async allow() {
			// this.$u.api.yysh.bindWxOpenid({
			// 	wxOpenid:uni.getStorageSync('loaclOpenId'),
			// 	userCode:this.vuex_user.userCode,
			// }).then(res => {
			// 		this.$u.vuex('vuex_user',{...this.vuex_user,wxOpenid:res.user.wxOpenid});
			// 		uni.setStorageSync('wxavatarUrl', this.avatarUrl)
			// 		setTimeout(() => {
			// 			uni.reLaunch({
			// 				url: '/pages/zfgs/index/notice/index'
			// 			});
			// 		}, 500);
		
			// 	});
			
			let _this = this;
					this.$u.api.getOpenId({
						code: _this.logincode,
						userCode: _this.vuex_user.userCode,
					}).then(res => {
						if (res.result == "true") {
							_this.$u.vuex('vuex_user', {
								..._this.vuex_user,
								wxOpenid: res.data
							});
							uni.setStorageSync('wxavatarUrl', _this.avatarUrl)
							// setTimeout(() => {
							// 	uni.setStorageSync('HBusername', this.username);
							// 	uni.setStorageSync('HBpassword', this.password);
							// 	uni.reLaunch({
							// 		url: '/pages/zfgs/index/notice/index'
							// 	});
							// }, 500);
							uni.setStorageSync('HBusername', this.username);
							uni.setStorageSync('HBpassword', this.password);
							uni.reLaunch({
								url: '/pages/m8/machine/list'
							});
							// this.subscribe()
						} else {
							_this.$u.toast(res.message);
						}
		
					});
		
				
		},
		 submit() {
			let _that = this
			if (this.username.length == 0) {
				this.$u.toast('请输入账号');
				return;
			}
			if (this.password.length == 0) {
				this.$u.toast('请输入密码');
				return;
			}
			this.$u.api.login({
				username: base64.btoa(this.username),
				password: base64.btoa(this.password),
				validCode: base64.btoa(this.validCode),
				param_deviceType: 'mobileApp',
				param_remember: this.remember,
				param_loginType:'2'
			})
			.then(async res => {
				console.log(res, 'res======');
				if (res.result == 'true') {
					this.$u.api.authInfo().then((res) => {
						this.setAuditList(res.stringPermissions)
					})
					this.$u.api.menuTree().then((res) =>{
						this.setMenuList(res)
					})
					uni.setStorageSync('HBusername', this.username);
					uni.setStorageSync('HBpassword', this.password);
					await this.getmenu();
					setTimeout(() => {
						uni.reLaunch({
							// url: this.vuex_tabBarList[0].pagePath
							// url: '/pages/sys/list',
							url: '/pages/tabbar/index',
							
						});
					}, 500);
					// if (!res.user.wxOpenid) {
					// 	uni.login({
					// 		provider: 'weixin',
					// 		success: function(loginRes) {
					// 			_that.logincode = loginRes.code
					// 		}
					// 	});
					// 	this.show = true
					// } else {
					// 	uni.setStorageSync('HBusername', this.username);
					// 	uni.setStorageSync('HBpassword', this.password);
					// 	uni.reLaunch({
					// 		url: '/pages/zfgs/index/notice/index'
					// 	});
					// }
				}else{
					uni.showModal({
						title: '提示',
						content: res.message || '未连接服务器',
						showCancel: false,
						success: function(res) {}
					});
					// this.$u.toast(res.message || '未连接服务器');
				}
				if (res.isValidCodeLogin){
					this.isValidCodeLogin = true;
					this.refreshImgValidCode();
				}
			});
		},
		wxLogin(res) {
			this.$u.toast('微信登录');
			let that = this;
			uni.login({
				provider: 'weixin',
				success: function(wxRes) {
					uni.showLoading('正在登录中...');
					console.error(wxRes);
					// 获取登录的token
					uni.setData('weixinToken', wxRes.code);
					// 获取登录的unionid 这个还是在开放平台做了 公众号 小程序 微信登录app关联才会有
					uni.setData('unionid', wxRes.authResult.unionid);
					// 获取openid
					uni.setData('weixinOpenid', wxRes.authResult.openid);
					// 这里吧数据全部提交给后台核验，有没有注册 注册了 后台代码会请求接口
					// String s = HttpClient.doGet("https://api.weixin.qq.com/sns/userinfo?access_token="
					//  + loginInfo.getToken() + "&openid=" + loginInfo.getOpenid()); 获取头像和昵称
					that.$u.postJson('/user/loginApp', {
						token: wxRes.authResult.access_token,
						unionid: wxRes.authResult.unionid,
						openid: wxRes.authResult.openid
					})
					.then(res => {
						if (res.status === 0) {
							// 绑定手机号直接登录
							that.getUserInfo(res.data.userId, res.data.uuid);
						} else {
							// 没有绑定手机号让绑定手机号
							uni.navigateTo({
								url: '/pages/public/wxmobile'
							});
						}
					});
				}
			});
		},
		qqLogin() {
			this.$u.toast('QQ 登录');
		},
		updateBaseUrl() {
			this.baseUrlList.forEach(item => {
				if (item.value == this.baseUrlValue){
					this.vuex_config.baseUrl = item.baseUrl;
					this.$u.vuex('vuex_baseUrl', this.vuex_config.baseUrl);
					this.$u.http.setConfig({ baseUrl: this.vuex_config.baseUrl });
					this.$u.toast('切换成功！');
					return;
				}
			});
		}
	}
};
</script>
<style lang="scss">
@import 'index.scss';

.logo {
	width: 500rpx;
	height: 260rpx;
	// background: rgba(59, 121, 235, 1);
	// box-shadow: 0rpx 5rpx 20rpx 5rpx rgba(45, 127, 235, 0.5);
	// border-radius: 50%;
	// border-radius: 10px;
	margin: 70rpx auto 10rpx auto;
}

.logo image {
	width: 500rpx;
	height: 260rpx;
	// border-radius: 50%;
}

.base-url js-select {
	width: 100%;
}

.button {
	margin: 30rpx auto 0;
	// background-color: #3E97B0 !important;
	background: linear-gradient(-90deg, #3E97B0, #3E97B0);
}

.footer {
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-items: center;
	text-align: center;
	color: #46afff;
	height: 40rpx;
	line-height: 40rpx;
	font-size: 35rpx;
	margin-top: 60rpx;
}

.footer text {
	font-size: 30rpx;
	margin-left: 25rpx;
	margin-right: 25rpx;
}

.oauth2 {
	display: flex;
	flex-direction: row;
	justify-content: space-around;
	margin: 55rpx 100rpx;

	image {
		height: 100rpx;
		width: 100rpx;
	}
}
</style>
