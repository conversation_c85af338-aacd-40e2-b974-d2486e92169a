/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 */
const config = {
	
	// 产品名称
	productName: '机加执行管理系统（M8）',
	
	// 公司名称
	companyName: '重庆轻企信息技术有限公司',
	
	// 管理基础路径
	adminPath: '/a',

	// 整改提交状态
	zgStatus: 2,
	// 整改结果
	zgResult:1,

	/**-1 未进场*/
	STATUS_NOT:'-1',
	/**0 延期中*/
	STATUS_YQ : "0",
	/**1 已通知进场*/
	STATUS_ENTZ: "1",
	/**2 已进场*/
	STATUS_EN:"2",
	/**3 进场通知作废*/
	STATUS_ENZF:"3",
	/**4 已通知撤场*/
	STATUS_EXTZ : "4",
	/**5 已撤场*/
	STATUS_EX:"5",
	// 施工状态 施工中为2
	projStatus:2,
	
	// 保留小数位数
	scaleNum:4, //件数
	scaleQty:4, //数量
	scalePrice:6, //单价
	scaleChangeRate:4, //换算率
	scaleArrQty:4,//收货数
	
	MoNotifyPrefix:'sc_',
	PoOrderPrefix:'po_',
	PuArrPrefix:'dh_',
	InventoryPrefix:'inv_',
	PositionPrefix:'hw_',
	TuoPanPrefix:'tp_',
	cbatchPrefix:'pc_',
	MoNotify:1,
	InvBarType:4,
	PosBarType:5,
	TuoPanBarType: 6,
	// needCheckStore检验现存量
	needCheckStore:1,
	jhStatus :{
		NO: 0,
		ING: 1,
		OVER: 2
	},
	//扫码类型常量 对应config.deviceType
	deviceTypeAPP : 1,
	deviceTypePDA : 2,
	xiangPrefix: 'X',
	hePrefix: 'H',
	pingPrefix: 'P',
	guanPrefix: 'G',
	fangPrefix: 'F',
	bizType_Fa: 0,
	bizType_Tui: 1,
	PosJustBusTypeEnum: {
		OrtherUp:"1", //其它上架
		OrtherDown:"2", //其它下架
		SoJh:"3", //销售拣货
		OrtherUp:"4", //拣货退回
		OrtherUp:"5", //销售退货
		OrtherUp:"6", //采购入库
		OrtherUp:"7", //采购退货
		Null:"99", //采购退货
	},
	MACHINEPrefix:'94-',
	POSITIONPrefix:'93-',
	INVPrefix:'92-',
	orderPrefix:'99-',
	userPrefix:'98-',
	checkMachineTypeEnum:{
		shangJi:'shangJi', //图纸上机
		xiaJi:'xiaJi', //图纸下机
		beginWork:'beginWork', //开始加工
		endWork:'endWork', //结束加工
		notifyFirstCheck:'notifyFirstCheck', //通知首检
		debug:'debug', //调试登记
		repCheck:'repCheck',
		erCiJiaGong:'erCiJiaGong',//二次加工
		neibuFanxiu:'neibuFanxiu',//内部返修
	},	
	menuConfig:{
		CLIENT_WEIXIN:'mp-weixin',
		CLIENT_QYWX:'wxwork',
		CLIENT_APP:'app',
	},
	flowTypeEnum:{
		beginWork:'beginWork',
		endWork:'endWork',	
		repCheck:'repCheck',
	},
/**
 * =====================================================================
 *打包需要更改的内容  start
*/
	// 更新检查配置：版本检查标识  android\pda
	appCode: 'pda',
	// 更新检查配置：产品版本号
	productVersion: 'V5.8',
	// 更新检查配置：内部版本号码
	appVersion: 5,
	// 最后更新日期设置（每次打包的时间）
	appUpdate: '2025/06/20 02:00',
	
	//扫码模式设置:	1是APP，为2是PDA
	deviceType: 2,
	
	//是否需要扫描员工工牌设置
	workScan:true, 
	
	//正式服务器IP地址设置
	// IP : "*************:8980",
	IP : "************:8980",
	//IP : "************:8980"
/**
 * ======================================================================
 *打包需要更改的内容 end
*/	
}
// 设置后台接口服务的基础地址
// config.baseUrl = 'https://'+config.IP+'/WMS';
config.baseUrl = 'http://'+config.IP+'/M8';
config.xtUrl = 'http://'+config.IP
// config.baseUrl = 'http://**************:8900/srm';
// 建议：打开下面注释，方便根据环境，自动设定服务地址
if (process.env.NODE_ENV === 'development'){
	// config.baseUrl = '/../js'; // 代理模式 vue.config.js 中找到 devServer 设置的地址
}

export default config;