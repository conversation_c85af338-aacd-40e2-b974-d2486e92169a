<template>
	<view class="process-execution">
		<view v-if="loading" class="loading-container">
			<u-loading mode="circle" size="60"></u-loading>
			<text class="loading-text">加载工序执行菜单中...</text>
		</view>
		
		<view v-else-if="processMenuList.length > 0" class="menu-container">
			<view v-for="category in processMenuList" :key="category.menuCode" class="menu-category">
				<view class="category-header">
					<u-icon name="/static/image/zfgs/index/icon@2x(6).png" size="80"></u-icon>
					<text class="category-title">{{ category.menuName }}</text>
				</view>
				
				<view class="menu-items">
					<template v-for="item in category.childList">
						<view
							:key="item.menuCode"
							class="menu-item"
							@click="navigateToPage(item)"
							v-if="item.isShow === true || category.isShow === true"
						>
						<view class="item-content">
							<view class="item-info">
								<text class="item-name">{{ item.menuName }}</text>
								<text class="item-desc">{{ item.menuTitle || item.remarks || '工序执行' }}</text>
							</view>
							<image 
								v-if="item.menuIcon" 
								:src="item.menuIcon" 
								class="item-icon"
								mode="aspectFit"
							/>
							<u-icon v-else name="play-circle" size="60" color="#409eff"></u-icon>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<view v-else class="empty-container">
			<u-icon name="inbox" size="120" color="#cccccc"></u-icon>
			<text class="empty-text">暂无工序执行菜单</text>
			<text class="empty-desc">请联系管理员配置工序执行菜单</text>
			<button @click="refreshMenu" class="refresh-btn">刷新菜单</button>
		</view>
		
		<!-- 缓存信息显示 -->
		<view v-if="showCacheInfo" class="cache-info">
			<text class="cache-title">缓存信息</text>
			<text class="cache-text">数据来源：{{ dataSource }}</text>
			<text class="cache-text">缓存时间：{{ cacheTime }}</text>
			<text class="cache-text">菜单数量：{{ processMenuList.length }}</text>
		</view>
	</view>
</template>

<script>
	import { mapState } from 'vuex';
	import menuMatcher from '@/utils/menuMatcher.js';
	
	export default {
		name: 'ProcessExecution',
		data() {
			return {
				loading: true,
				processMenuList: [],
				dataSource: '',
				cacheTime: '',
				showCacheInfo: false // 开发时可设为 true 查看缓存信息
			};
		},
		computed: {
			...mapState(['vuex_processMenuList'])
		},
		mounted() {
			console.log('工序执行组件已挂载');
			this.loadProcessMenu();
		},
		methods: {
			/**
			 * 加载工序执行菜单（优先从缓存读取，支持 isShow 过滤）
			 */
			async loadProcessMenu() {
				console.log('开始加载工序执行菜单...');
				this.loading = true;

				try {
					// 方法1: 优先从匹配后的本地存储读取
					const matchedMenu = uni.getStorageSync('matchedProcessMenus');
					if (matchedMenu && matchedMenu.length > 0) {
						console.log('✓ 从匹配后的本地存储加载工序执行菜单:', matchedMenu);
						// 过滤出 isShow 为 true 的菜单项
						this.processMenuList = this.filterVisibleProcessMenus(matchedMenu);
						this.dataSource = '匹配后缓存';
						this.cacheTime = this.formatCacheTime();
						this.loading = false;
						return;
					}

					// 方法2: 从 Vuex 读取
					if (this.vuex_processMenuList && this.vuex_processMenuList.length > 0) {
						console.log('✓ 从 Vuex 加载工序执行菜单:', this.vuex_processMenuList);
						// 过滤出 isShow 为 true 的菜单项
						this.processMenuList = this.filterVisibleProcessMenus(this.vuex_processMenuList);
						this.dataSource = 'Vuex 状态';
						this.cacheTime = this.formatCacheTime();
						this.loading = false;
						return;
					}

					// 方法3: 从旧版本缓存读取（兼容性）
					const cachedMenu = uni.getStorageSync('processMenuList');
					if (cachedMenu && cachedMenu.length > 0) {
						console.log('✓ 从旧版本缓存加载工序执行菜单:', cachedMenu);
						this.processMenuList = cachedMenu;
						this.dataSource = '旧版本缓存';
						this.cacheTime = this.formatCacheTime();
						this.loading = false;
						return;
					}

					// 方法4: 实时从 API 获取（备用方案）
					await this.loadFromAPI();
					
				} catch (error) {
					console.error('❌ 加载工序执行菜单失败:', error);
					this.processMenuList = [];
					this.dataSource = '加载失败';
				} finally {
					this.loading = false;
				}
			},

			/**
			 * 过滤出 isShow 为 true 的工序执行菜单项
			 * @param {Array} processMenuList - 工序执行菜单列表
			 * @returns {Array} 过滤后的菜单列表
			 */
			filterVisibleProcessMenus(processMenuList) {
				if (!processMenuList || !Array.isArray(processMenuList)) {
					return [];
				}

				const filteredMenus = processMenuList.map(category => {
					if (!category.childList || !Array.isArray(category.childList)) {
						return category;
					}

					// 过滤出 isShow 为 true 的子菜单项
					const visibleChildren = category.childList.filter(child => child.isShow === true);

					// 如果有可见的子菜单项，返回包含这些子菜单的分类
					if (visibleChildren.length > 0) {
						return {
							...category,
							childList: visibleChildren
						};
					}

					// 如果没有可见的子菜单项，返回 null（稍后会被过滤掉）
					return null;
				}).filter(category => category !== null); // 移除没有可见子菜单的分类

				console.log('过滤工序执行菜单完成:', {
					原始分类数: processMenuList.length,
					过滤后分类数: filteredMenus.length,
					可见菜单项总数: filteredMenus.reduce((total, category) => total + (category.childList?.length || 0), 0)
				});

				return filteredMenus;
			},

			/**
			 * 从 API 实时加载工序执行菜单（使用菜单匹配器）
			 */
			async loadFromAPI() {
				console.log('从 API 实时加载工序执行菜单...');

				try {
					const apiMenuList = await this.$u.api.m8.findMenuList({
						parentFuncFlag: 'menu:m8:gongxvzhixin',
						clientFlag: this.vuex_config.menuConfig.CLIENT_APP,
					});

					if (apiMenuList && apiMenuList.length > 0) {
						console.log('✓ API 加载工序执行菜单成功:', apiMenuList.length, '项');

						// 使用菜单匹配器进行匹配
						const matchedProcessMenus = menuMatcher.matchProcessMenus(apiMenuList);

						// 过滤出 isShow 为 true 的菜单项
						this.processMenuList = this.filterVisibleProcessMenus(matchedProcessMenus);
						this.dataSource = 'API 实时匹配';
						this.cacheTime = this.formatCacheTime();

						// 更新缓存（保存匹配后的完整数据）
						uni.setStorageSync('matchedProcessMenus', matchedProcessMenus);
						uni.setStorageSync('processMenuList', apiMenuList); // 保留原始数据用于兼容
						this.$store.commit('setProcessMenuList', matchedProcessMenus);

						console.log('✓ 工序执行菜单匹配完成，可见菜单:', this.processMenuList.length, '个分类');

					} else {
						console.warn('⚠️ API 未返回工序执行菜单数据，尝试使用默认配置');
						this.loadDefaultProcessMenus();
					}
					
				} catch (error) {
					console.error('❌ API 加载工序执行菜单失败:', error);
					this.processMenuList = [];
					this.dataSource = 'API 错误';
					throw error;
				}
			},
			
			/**
			 * 刷新菜单
			 */
			async refreshMenu() {
				console.log('手动刷新工序执行菜单...');
				
				// 清除缓存
				uni.removeStorageSync('processMenuList');
				this.$store.commit('setProcessMenuList', []);
				
				// 重新加载
				await this.loadProcessMenu();
				
				uni.showToast({
					title: '菜单已刷新',
					icon: 'success'
				});
			},
			
			/**
			 * 导航到指定页面
			 */
			navigateToPage(item) {
				console.log('导航到工序执行页面:', item);
				
				if (!item.menuUrl) {
					uni.showToast({
						title: '页面路径未配置',
						icon: 'error'
					});
					return;
				}
				
				uni.navigateTo({
					url: item.menuUrl,
					success: () => {
						console.log('✓ 导航成功:', item.menuUrl);
					},
					fail: (error) => {
						console.error('❌ 导航失败:', error);
						uni.showToast({
							title: '页面跳转失败',
							icon: 'error'
						});
					}
				});
			},

			/**
			 * 加载默认工序执行菜单（从 menu.json 获取 isShow=true 的项）
			 */
			loadDefaultProcessMenus() {
				console.log('加载默认工序执行菜单（从 menu.json 获取 isShow=true 的项）');

				try {
					// 从 menu.json 中获取 isShow 为 true 的工序执行菜单项
					const defaultProcessMenus = menuMatcher.getDefaultProcessConfig();

					if (defaultProcessMenus && defaultProcessMenus.length > 0) {
						// 过滤出 isShow 为 true 的菜单项
						this.processMenuList = this.filterVisibleProcessMenus(defaultProcessMenus);
						this.dataSource = 'menu.json 默认';
						this.cacheTime = this.formatCacheTime();

						console.log('✓ 使用 menu.json 中的默认工序执行菜单:', this.processMenuList.length, '个分类');
					} else {
						console.warn('⚠️ menu.json 中没有默认工序执行菜单');
						this.processMenuList = [];
						this.dataSource = '无数据';
					}
				} catch (error) {
					console.error('加载默认工序执行菜单失败:', error);
					this.processMenuList = [];
					this.dataSource = '加载失败';
				}
			},

			/**
			 * 格式化缓存时间
			 */
			formatCacheTime() {
				const now = new Date();
				return now.toLocaleString('zh-CN', {
					year: 'numeric',
					month: '2-digit',
					day: '2-digit',
					hour: '2-digit',
					minute: '2-digit',
					second: '2-digit'
				});
			}
		}
	};
</script>

<style lang="scss" scoped>
	.process-execution {
		padding: 20rpx;
		background-color: #f8f8f8;
		min-height: 100vh;
	}
	
	.loading-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		height: 60vh;
		
		.loading-text {
			margin-top: 30rpx;
			font-size: 28rpx;
			color: #666666;
		}
	}
	
	.menu-container {
		.menu-category {
			margin-bottom: 40rpx;
			
			.category-header {
				display: flex;
				align-items: center;
				padding: 30rpx 20rpx;
				background-color: #ffffff;
				border-radius: 16rpx 16rpx 0 0;
				border-bottom: 1rpx solid #e0e0e0;
				
				.category-title {
					margin-left: 20rpx;
					font-size: 36rpx;
					font-weight: bold;
					color: #333333;
				}
			}
			
			.menu-items {
				background-color: #ffffff;
				border-radius: 0 0 16rpx 16rpx;
				
				.menu-item {
					padding: 30rpx 20rpx;
					border-bottom: 1rpx solid #f0f0f0;
					
					&:last-child {
						border-bottom: none;
					}
					
					.item-content {
						display: flex;
						align-items: center;
						justify-content: space-between;
						
						.item-info {
							flex: 1;
							
							.item-name {
								display: block;
								font-size: 32rpx;
								font-weight: bold;
								color: #333333;
								margin-bottom: 10rpx;
							}
							
							.item-desc {
								display: block;
								font-size: 26rpx;
								color: #666666;
							}
						}
						
						.item-icon {
							width: 60rpx;
							height: 60rpx;
							margin-left: 20rpx;
						}
					}
					
					&:active {
						background-color: #f0f0f0;
					}
				}
			}
		}
	}
	
	.empty-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		height: 60vh;
		
		.empty-text {
			margin-top: 30rpx;
			font-size: 32rpx;
			color: #333333;
			font-weight: bold;
		}
		
		.empty-desc {
			margin-top: 15rpx;
			font-size: 26rpx;
			color: #666666;
		}
		
		.refresh-btn {
			margin-top: 40rpx;
			padding: 20rpx 40rpx;
			background-color: #409eff;
			color: #ffffff;
			border: none;
			border-radius: 8rpx;
			font-size: 28rpx;
		}
	}
	
	.cache-info {
		margin-top: 40rpx;
		padding: 20rpx;
		background-color: #ffffff;
		border-radius: 16rpx;
		border: 1rpx solid #e0e0e0;
		
		.cache-title {
			display: block;
			font-size: 28rpx;
			font-weight: bold;
			color: #333333;
			margin-bottom: 15rpx;
		}
		
		.cache-text {
			display: block;
			font-size: 24rpx;
			color: #666666;
			margin-bottom: 8rpx;
		}
	}
</style>
