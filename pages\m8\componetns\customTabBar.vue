<template>
	<view class="custom-tab-bar">
		<view v-for="(item, index) in vuex_tabBarList" :key="index" class="tab-item"
			:class="{ 'tab-item-active': currentIndex === item.index }" @click="switchTab(item.pagePath)">
			<image :src="currentIndex === item.index ? item.selectedIconPath : item.iconPath" mode="aspectFit"
				class="tab-icon" />
			<text class="tab-text">{{ item.text }}</text>
			<!-- 新增 badge 显示 -->
			<view v-if="item.badge && item.badge.show" class="tab-badge">
				<text v-if="item.badge.value > 99">99+</text>
				<text v-else>{{ item.badge.value }}</text>
			</view>

		</view>
	</view>
</template>
<script>
	export default {
		name: 'customTabBar',
		props: {
			currentIndex: {
				type: Number,
				default: 0
			}
		},
		data() {
			return {
				tabBarList: [
				],
			}
		},
		onShow() {
			
		},
		async mounted() {
			
		},
		methods: {
			switchTab(pagePath) {
				console.log(pagePath)
				this.$emit('changeTab', pagePath);
			},
			// getcountData() {
			// 	this.$u.api.bpm.countData().then((res) => {
			// 		// 更改tabBarList 数组里面 text 为 "待办事项" 的badge的value
			// 		this.tabBarList.forEach((item, index) => {
			// 			if (item.text == '待办事项') {
			// 				if (res.data > 0) {
			// 					this.$set(item, 'badge', {
			// 						...item.badge,
			// 						show: true,
			// 						value: res.data
			// 					});
			// 				}
			// 			}
			// 		})
			// 		this.$forceUpdate();
			// 	})
			// },
			// getMsgCount() {
			// 	this.$u.api.zfgs.msgPushGetMsgCount().then((res) => {
			// 		// 更改tabBarList 数组里面 text 为 "待办事项" 的badge的value
			// 		this.tabBarList.forEach((item, index) => {
			// 			if (item.text == '消息通知') {
			// 				if (res.data > 0) {
			// 					this.$set(item, 'badge', {
			// 						...item.badge,
			// 						show: true,
			// 						value: res.data
			// 					});
			// 				}
			// 			}
			// 		})
			// 		this.$forceUpdate();
			// 	})
			// },
		}
	};
</script>
<style lang="scss" scoped>
	.custom-tab-bar {
		display: flex;
		justify-content: space-around;
		align-items: center;
		width: 100%;
		height: 108rpx;
		background-color: #ffffff;
		position: fixed;
		bottom: 0;
		left: 0;
		box-sizing: border-box;
		z-index: 999;
	}

	.tab-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		position: relative;

		.tab-badge {
			position: absolute;
			top: -22rpx;
			right: -22rpx;
			width: 44rpx;
			height: 44rpx;
			border-radius: 22rpx;
			background-color: red;
			display: flex;
			align-items: center;
			justify-content: center;
			color: #ffffff;
			font-size: 22rpx;
		}
	}

	.tab-icon {
		width: 60rpx;
		height: 60rpx;
		margin-bottom: 8rpx;
	}

	.tab-text {
		font-size: 32rpx;
		color: #909399;
	}

	.tab-item-active {
		.tab-text {
			color: #1296db;
		}
	}
</style>