/**
 * 菜单匹配工具类
 * 用于处理 API 数据与本地 menu.json 的匹配逻辑
 */

// 导入本地菜单配置
import menuConfig from '@/menu.json'

class MenuMatcher {
	constructor() {
		this.localMenuList = menuConfig.tabMenuList || [];
		this.processMenuList = menuConfig.processMenuList || [];
		this.warehouseMenuList = menuConfig.WarehouseMenuList || [];
		this.workshopMenuList = menuConfig.cwMenuList || [];
		this.machineMenuList = menuConfig.machineMenuList || [];
	}

	/**
	 * 匹配 API 数据与本地菜单配置
	 * @param {Array} apiMenuList - API 返回的菜单数据
	 * @returns {Array} 匹配后的菜单配置
	 */
	matchTabBarMenus(apiMenuList) {
		if (!apiMenuList || !Array.isArray(apiMenuList)) {
			console.warn('API 菜单数据无效，返回默认配置');
			return this.getDefaultTabBarConfig();
		}

		// 创建本地菜单的副本，保留原始 isShow 值
		const matchedMenuList = this.localMenuList.map(localItem => ({
			...localItem,
			// 如果本地菜单项的 isShow 已经是 true，则保持 true
			isShow: localItem.isShow === true ? true : false
		}));

		// 遍历 API 菜单，匹配本地配置
		apiMenuList.forEach((apiItem) => {
			const apiExtendS2 = apiItem.extend?.extendS2;

			if (!apiExtendS2) {
				return;
			}

			// 在本地菜单中查找匹配项
			const matchedIndex = matchedMenuList.findIndex(localItem => 
				localItem.extend?.extendS2 === apiExtendS2
			);

			if (matchedIndex !== -1) {
				const matchedItem = matchedMenuList[matchedIndex];

				// 如果本地菜单项的 isShow 不是 true，则通过 API 匹配设置为 true
				if (matchedItem.isShow !== true) {
					matchedMenuList[matchedIndex].isShow = true;
				}

				// 可选：使用 API 数据更新本地配置（但不覆盖本地为 true 的 isShow）
				if (apiItem.menuName && apiItem.menuName !== matchedItem.text) {
					matchedMenuList[matchedIndex].text = apiItem.menuName;
				}
				if (apiItem.menuUrl && apiItem.menuUrl !== matchedItem.pagePath) {
					matchedMenuList[matchedIndex].pagePath = this.normalizePagePath(apiItem.menuUrl);
				}
			}
		});

		// 过滤出 isShow 为 true 的菜单项
		const visibleMenus = matchedMenuList.filter(item => item.isShow);

		// 检查"我的"菜单是否已显示（通常在 menu.json 中已设置为 true）
		const myMenuExists = visibleMenus.some(item =>
			item.extend?.extendS2 === 'menue:user:my'
		);

		// 如果"我的"菜单不在可见列表中，强制添加（备用保护）
		if (!myMenuExists) {
			const myMenuIndex = matchedMenuList.findIndex(item =>
				item.extend?.extendS2 === 'menue:user:my'
			);
			if (myMenuIndex !== -1) {
				matchedMenuList[myMenuIndex].isShow = true;
				visibleMenus.push(matchedMenuList[myMenuIndex]);
			}
		}

		// 限制最多5个菜单项
		const finalMenus = visibleMenus.slice(0, 5);

		return {
			allMenus: matchedMenuList,
			visibleMenus: finalMenus
		};
	}

	/**
	 * 匹配工序执行菜单
	 * @param {Array} apiProcessMenuList - API 返回的工序执行菜单数据
	 * @returns {Array} 匹配后的工序执行菜单配置
	 */
	matchProcessMenus(apiProcessMenuList) {
		if (!apiProcessMenuList || !Array.isArray(apiProcessMenuList)) {
			console.warn('API 工序菜单数据无效，返回默认配置');
			return this.getDefaultProcessConfig();
		}

		// 创建本地工序菜单的副本，保留原始 isShow 值
		const matchedProcessList = JSON.parse(JSON.stringify(this.processMenuList));

		// 重置所有 isShow 为 false，但保留原本为 true 的项
		this.resetProcessMenuShowButKeepOriginalTrue(matchedProcessList);

		// 遍历 API 工序菜单，匹配本地配置
		apiProcessMenuList.forEach((apiItem) => {
			const apiExtendS2 = apiItem.extend?.extendS2;

			if (!apiExtendS2) {
				return;
			}

			// 在本地工序菜单中查找匹配项
			matchedProcessList.forEach(category => {
				// 首先检查是否匹配分类级别的菜单
				if (category.extend?.extendS2 === apiExtendS2) {
					// 匹配到分类级别的菜单
					if (category.isShow !== true) {
						category.isShow = true;
					}

					// 更新分类信息
					if (apiItem.menuName && apiItem.menuName !== category.menuName) {
						category.menuName = apiItem.menuName;
					}

					// 如果 API 项目也有 childList，处理子菜单匹配
					if (apiItem.childList && Array.isArray(apiItem.childList) && category.childList) {
						apiItem.childList.forEach((apiChild) => {
							const apiChildExtendS2 = apiChild.extend?.extendS2;

							if (apiChildExtendS2) {
								// 在本地分类的子菜单中查找匹配项
								category.childList.forEach((localChild) => {
									if (localChild.extend?.extendS2 === apiChildExtendS2) {
										if (localChild.isShow !== true) {
											localChild.isShow = true;
										}

										// 更新子菜单信息
										if (apiChild.menuName && apiChild.menuName !== localChild.menuName) {
											localChild.menuName = apiChild.menuName;
										}
									}
								});
							}
						});
					}

					return;
				}

				// 然后检查子菜单项
				if (category.childList) {
					category.childList.forEach((childItem) => {
						if (childItem.extend?.extendS2 === apiExtendS2) {
							// 如果本地菜单项的 isShow 不是 true，则通过 API 匹配设置为 true
							if (childItem.isShow !== true) {
								childItem.isShow = true;
							}

							// 可选：使用 API 数据更新本地配置（但不覆盖本地为 true 的 isShow）
							if (apiItem.menuName && apiItem.menuName !== childItem.menuName) {
								childItem.menuName = apiItem.menuName;
							}
						}
					});
				}
			});

		});

		return matchedProcessList;
	}

	/**
	 * 匹配仓库菜单
	 * @param {Array} apiWarehouseMenuList - API 返回的仓库菜单数据
	 * @returns {Array} 匹配后的仓库菜单配置
	 */
	matchWarehouseMenus(apiWarehouseMenuList) {
		if (!apiWarehouseMenuList || !Array.isArray(apiWarehouseMenuList)) {
			console.warn('API 仓库菜单数据无效，返回默认配置');
			return this.getDefaultWarehouseConfig();
		}

		// 创建本地仓库菜单的副本，保留原始 isShow 值
		const matchedWarehouseList = JSON.parse(JSON.stringify(this.warehouseMenuList));

		// 重置所有 isShow 为 false，但保留原本为 true 的项
		this.resetWarehouseMenuShowButKeepOriginalTrue(matchedWarehouseList);

		// 遍历 API 仓库菜单，匹配本地配置
		apiWarehouseMenuList.forEach((apiItem) => {
			const apiExtendS2 = apiItem.extend?.extendS2;

			// 检查 API 项目是否有 childList（分类级别的数据）
			if (apiItem.childList && Array.isArray(apiItem.childList) && apiItem.childList.length > 0) {
				// 处理 API 分类的子菜单
				apiItem.childList.forEach((apiChild) => {
					const apiChildExtendS2 = apiChild.extend?.extendS2;

					if (!apiChildExtendS2) {
						return;
					}

					// 在本地菜单中查找匹配的子菜单项
					matchedWarehouseList.forEach(localCategory => {
						if (localCategory.childList) {
							localCategory.childList.forEach((localChild) => {
								if (localChild.extend?.extendS2 === apiChildExtendS2) {
									if (localChild.isShow !== true) {
										localChild.isShow = true;
									}

									// 更新子菜单信息
									if (apiChild.menuName && apiChild.menuName !== localChild.menuName) {
										localChild.menuName = apiChild.menuName;
									}
								}
							});
						}
					});
				});

				return; // 处理完分类的子菜单后，跳过后续的单项匹配逻辑
			}

			// 如果没有 childList，按原来的逻辑处理单个菜单项
			if (!apiExtendS2) {
				return;
			}

			// 在本地仓库菜单中查找匹配项
			matchedWarehouseList.forEach(category => {

				// 首先检查是否匹配分类级别的菜单
				if (category.extend?.extendS2 === apiExtendS2) {
					// 匹配到分类级别的菜单
					if (category.isShow !== true) {
						category.isShow = true;
					}

					// 更新分类信息
					if (apiItem.menuName && apiItem.menuName !== category.menuName) {
						category.menuName = apiItem.menuName;
					}

					// 如果 API 项目也有 childList，处理子菜单匹配
					if (apiItem.childList && Array.isArray(apiItem.childList) && category.childList) {
						apiItem.childList.forEach((apiChild) => {
							const apiChildExtendS2 = apiChild.extend?.extendS2;

							if (apiChildExtendS2) {
								// 在本地分类的子菜单中查找匹配项
								category.childList.forEach((localChild) => {
									if (localChild.extend?.extendS2 === apiChildExtendS2) {
										if (localChild.isShow !== true) {
											localChild.isShow = true;
										}

										// 更新子菜单信息
										if (apiChild.menuName && apiChild.menuName !== localChild.menuName) {
											localChild.menuName = apiChild.menuName;
										}
									}
								});
							}
						});
					}
					return;
				}

				// 然后检查子菜单项（处理 API 直接返回子菜单项的情况）
				if (category.childList) {
					category.childList.forEach((childItem) => {
						if (childItem.extend?.extendS2 === apiExtendS2) {
							// 如果本地菜单项的 isShow 不是 true，则通过 API 匹配设置为 true
							if (childItem.isShow !== true) {
								childItem.isShow = true;
							}

							// 可选：使用 API 数据更新本地配置（但不覆盖本地为 true 的 isShow）
							if (apiItem.menuName && apiItem.menuName !== childItem.menuName) {
								childItem.menuName = apiItem.menuName;
							}
						}
					});
				}
			});
		});

		return matchedWarehouseList;
	}

	/**
	 * 匹配车间菜单
	 * @param {Array} apiWorkshopMenuList - API 返回的车间菜单数据
	 * @returns {Array} 匹配后的车间菜单配置
	 */
	matchWorkshopMenus(apiWorkshopMenuList) {
		if (!apiWorkshopMenuList || !Array.isArray(apiWorkshopMenuList)) {
			console.warn('API 车间菜单数据无效，返回默认配置');
			return this.getDefaultWorkshopConfig();
		}

		// 创建本地车间菜单的副本，保留原始 isShow 值
		const matchedWorkshopList = JSON.parse(JSON.stringify(this.workshopMenuList));

		// 重置所有 isShow 为 false，但保留原本为 true 的项
		this.resetWorkshopMenuShowButKeepOriginalTrue(matchedWorkshopList);

		// 遍历 API 车间菜单，匹配本地配置
		apiWorkshopMenuList.forEach((apiItem) => {
			const apiExtendS2 = apiItem.extend?.extendS2;

			// 检查 API 项目是否有 childList（分类级别的数据）
			if (apiItem.childList && Array.isArray(apiItem.childList) && apiItem.childList.length > 0) {
				// 处理 API 分类的子菜单
				apiItem.childList.forEach((apiChild) => {
					const apiChildExtendS2 = apiChild.extend?.extendS2;

					if (!apiChildExtendS2) {
						return;
					}

					// 在本地菜单中查找匹配的子菜单项
					matchedWorkshopList.forEach(localCategory => {
						if (localCategory.childList) {
							localCategory.childList.forEach((localChild) => {
								if (localChild.extend?.extendS2 === apiChildExtendS2) {
									if (localChild.isShow !== true) {
										localChild.isShow = true;
									}

									// 更新子菜单信息
									if (apiChild.menuName && apiChild.menuName !== localChild.menuName) {
										localChild.menuName = apiChild.menuName;
									}
								}
							});
						}
					});
				});

				return; // 处理完分类的子菜单后，跳过后续的单项匹配逻辑
			}

			// 如果没有 childList，按原来的逻辑处理单个菜单项
			if (!apiExtendS2) {
				return;
			}

			// 在本地车间菜单中查找匹配项
			matchedWorkshopList.forEach(category => {

				// 首先检查是否匹配分类级别的菜单
				if (category.extend?.extendS2 === apiExtendS2) {
					// 匹配到分类级别的菜单
					if (category.isShow !== true) {
						category.isShow = true;
					}

					// 更新分类信息
					if (apiItem.menuName && apiItem.menuName !== category.menuName) {
						category.menuName = apiItem.menuName;
					}

					return;
				}

				// 然后检查子菜单项（处理 API 直接返回子菜单项的情况）
				if (category.childList) {
					category.childList.forEach((childItem) => {
						if (childItem.extend?.extendS2 === apiExtendS2) {
							// 如果本地菜单项的 isShow 不是 true，则通过 API 匹配设置为 true
							if (childItem.isShow !== true) {
								childItem.isShow = true;
							}

							// 可选：使用 API 数据更新本地配置（但不覆盖本地为 true 的 isShow）
							if (apiItem.menuName && apiItem.menuName !== childItem.menuName) {
								childItem.menuName = apiItem.menuName;
							}
						}
					});
				}
			});
		});

		return matchedWorkshopList;
	}

	/**
	 * 匹配机床菜单
	 * @param {Array} apiMachineMenuList - API 返回的机床菜单数据
	 * @returns {Array} 匹配后的机床菜单配置
	 */
	matchMachineMenus(apiMachineMenuList) {
		if (!apiMachineMenuList || !Array.isArray(apiMachineMenuList)) {
			console.warn('API 机床菜单数据无效，返回默认配置');
			return this.getDefaultMachineConfig();
		}

		// 创建本地机床菜单的副本，保留原始 isShow 值
		const matchedMachineList = JSON.parse(JSON.stringify(this.machineMenuList));

		// 重置所有 isShow 为 false，但保留原本为 true 的项
		this.resetMachineMenuShowButKeepOriginalTrue(matchedMachineList);

		// 遍历 API 机床菜单，匹配本地配置
		apiMachineMenuList.forEach((apiItem) => {
			const apiExtendS2 = apiItem.extend?.extendS2;

			if (!apiExtendS2) {
				return;
			}

			// 在本地机床菜单中查找匹配项
			matchedMachineList.forEach((localItem) => {

				if (localItem.extend?.extendS2 === apiExtendS2) {
					// 如果本地菜单项的 isShow 不是 true，则通过 API 匹配设置为 true
					if (localItem.isShow !== true) {
						localItem.isShow = true;
					}

					// 可选：使用 API 数据更新本地配置（但不覆盖本地为 true 的 isShow）
					if (apiItem.menuName && apiItem.menuName !== localItem.menuName) {
						localItem.menuName = apiItem.menuName;
					}
					if (apiItem.menuIcon && apiItem.menuIcon !== localItem.menuIcon) {
						localItem.menuIcon = apiItem.menuIcon;
					}
					if (apiItem.funcType && apiItem.funcType !== localItem.funcType) {
						localItem.funcType = apiItem.funcType;
					}
				}
			});
		});

		return matchedMachineList;
	}

	/**
	 * 重置工序菜单的 isShow 状态
	 */
	resetProcessMenuShow(processMenuList) {
		processMenuList.forEach(category => {
			if (category.childList) {
				category.childList.forEach(childItem => {
					childItem.isShow = false;
				});
			}
		});
	}

	/**
	 * 重置工序菜单的 isShow 状态，但保留原本为 true 的项
	 */
	resetProcessMenuShowButKeepOriginalTrue(processMenuList) {
		// 获取原始菜单配置
		const originalProcessMenuList = this.processMenuList || [];

		processMenuList.forEach((category, categoryIndex) => {
			console.log(`重置分类 ${categoryIndex + 1}: ${category.menuName} (${category.extend?.extendS2})`);

			// 处理分类级别的 isShow 状态
			let categoryOriginalIsShow = false;

			// 查找原始配置中对应的分类
			originalProcessMenuList.forEach(originalCategory => {
				if (originalCategory.extend?.extendS2 === category.extend?.extendS2) {
					categoryOriginalIsShow = originalCategory.isShow === true;
					console.log(`  找到原始分类匹配: ${originalCategory.menuName} (isShow: ${originalCategory.isShow})`);
				}
			});

			// 如果原始配置中该分类的 isShow 不是 true，则重置为 false
			if (!categoryOriginalIsShow) {
				category.isShow = false;
				console.log(`  重置分类 ${category.menuName} isShow 为 false`);
			}

			if (categoryOriginalIsShow) {
				console.log(`  保留原本分类显示状态: ${category.menuName} (${category.extend?.extendS2})`);
			}

			// 处理子菜单项的 isShow 状态
			if (category.childList) {
				console.log(`  处理分类 ${category.menuName} 的 ${category.childList.length} 个子菜单项`);
				category.childList.forEach((childItem, childIndex) => {
					console.log(`    重置子菜单 ${childIndex + 1}: ${childItem.menuName} (${childItem.extend?.extendS2})`);

					// 通过 extend.extendS2 查找原始配置中对应的项
					let originalIsShow = false;

					// 在所有原始分类中查找匹配的菜单项
					originalProcessMenuList.forEach(originalCategory => {
						if (originalCategory.childList) {
							originalCategory.childList.forEach(originalChild => {
								if (originalChild.extend?.extendS2 === childItem.extend?.extendS2) {
									originalIsShow = originalChild.isShow === true;
									console.log(`      找到原始子菜单匹配: ${originalChild.menuName} (isShow: ${originalChild.isShow})`);
								}
							});
						}
					});

					// 如果原始配置中该项的 isShow 不是 true，则重置为 false
					if (!originalIsShow) {
						childItem.isShow = false;
						console.log(`      重置子菜单 ${childItem.menuName} isShow 为 false`);
					}

					if (originalIsShow) {
						console.log(`  保留原本子菜单显示状态: ${childItem.menuName} (${childItem.extend?.extendS2})`);
					}
				});
			} else {
				console.log(`  分类 ${category.menuName} 没有子菜单项`);
			}
		});

		console.log('工序菜单 isShow 状态重置完成，保留了原本为 true 的项');
	}

	/**
	 * 保存匹配结果到本地存储
	 * @param {Array} tabBarMenus - TabBar 菜单配置
	 * @param {Array} processMenus - 工序执行菜单配置
	 * @param {Array} warehouseMenus - 仓库菜单配置
	 * @param {Array} workshopMenus - 车间菜单配置
	 * @param {Array} machineMenus - 机床菜单配置
	 */
	saveToLocalStorage(tabBarMenus, processMenus, warehouseMenus, workshopMenus, machineMenus) {
		try {
			// 保存 TabBar 菜单
			if (tabBarMenus) {
				uni.setStorageSync('matchedTabBarMenus', tabBarMenus);
				console.log('✓ TabBar 菜单已保存到本地存储');
			}

			// 保存工序执行菜单
			if (processMenus) {
				uni.setStorageSync('matchedProcessMenus', processMenus);
				console.log('✓ 工序执行菜单已保存到本地存储');
			}

			// 保存仓库菜单
			if (warehouseMenus) {
				uni.setStorageSync('matchedWarehouseMenus', warehouseMenus);
				console.log('✓ 仓库菜单已保存到本地存储');
			}

			// 保存车间菜单
			if (workshopMenus) {
				uni.setStorageSync('matchedWorkshopMenus', workshopMenus);
				console.log('✓ 车间菜单已保存到本地存储');
			}

			// 保存机床菜单
			if (machineMenus) {
				uni.setStorageSync('matchedMachineMenus', machineMenus);
				console.log('✓ 机床菜单已保存到本地存储');
			}

			// 保存匹配时间戳
			uni.setStorageSync('menuMatchTimestamp', Date.now());

		} catch (error) {
			console.error('保存菜单到本地存储失败:', error);
		}
	}

	/**
	 * 从本地存储加载匹配结果
	 * @returns {Object} 本地存储的菜单配置
	 */
	loadFromLocalStorage() {
		try {
			const tabBarMenus = uni.getStorageSync('matchedTabBarMenus');
			const processMenus = uni.getStorageSync('matchedProcessMenus');
			const warehouseMenus = uni.getStorageSync('matchedWarehouseMenus');
			const workshopMenus = uni.getStorageSync('matchedWorkshopMenus');
			const machineMenus = uni.getStorageSync('matchedMachineMenus');
			const timestamp = uni.getStorageSync('menuMatchTimestamp');

			console.log('从本地存储加载菜单配置:', {
				tabBarCount: tabBarMenus?.length || 0,
				processCount: processMenus?.length || 0,
				warehouseCount: warehouseMenus?.length || 0,
				workshopCount: workshopMenus?.length || 0,
				machineCount: machineMenus?.length || 0,
				timestamp: timestamp ? new Date(timestamp).toLocaleString() : '无'
			});

			return {
				tabBarMenus,
				processMenus,
				warehouseMenus,
				workshopMenus,
				machineMenus,
				timestamp
			};
		} catch (error) {
			console.error('从本地存储加载菜单失败:', error);
			return {};
		}
	}

	/**
	 * 获取默认 TabBar 配置
	 */
	getDefaultTabBarConfig() {
		return this.localMenuList.filter(item =>
			item.extend?.extendS2 === 'menu:user:my'
		);
	}

	/**
	 * 获取默认工序执行配置
	 */
	getDefaultProcessConfig() {
		return this.processMenuList;
	}

	/**
	 * 标准化页面路径
	 */
	normalizePagePath(path) {
		if (!path) return '';
		// 移除开头的斜杠
		return path.startsWith('/') ? path.substring(1) : path;
	}

	/**
	 * 重置仓库菜单的 isShow 状态，但保留原本为 true 的项
	 */
	resetWarehouseMenuShowButKeepOriginalTrue(warehouseMenuList) {
		console.log('开始重置仓库菜单 isShow 状态，保留原本为 true 的项');

		// 获取原始菜单配置
		const originalWarehouseMenuList = this.warehouseMenuList || [];

		warehouseMenuList.forEach((category, categoryIndex) => {
			console.log(`重置仓库分类 ${categoryIndex + 1}: ${category.menuName} (${category.extend?.extendS2})`);

			// 处理分类级别的 isShow 状态
			let categoryOriginalIsShow = false;

			// 查找原始配置中对应的分类
			originalWarehouseMenuList.forEach(originalCategory => {
				if (originalCategory.extend?.extendS2 === category.extend?.extendS2) {
					categoryOriginalIsShow = originalCategory.isShow === true;
					console.log(`  找到原始仓库分类匹配: ${originalCategory.menuName} (isShow: ${originalCategory.isShow})`);
				}
			});

			// 如果原始配置中该分类的 isShow 是 true，则保持 true，不做任何处理
			if (categoryOriginalIsShow) {
				category.isShow = true; // 确保保持为 true
				console.log(`  ✓ 保留原本仓库分类显示状态: ${category.menuName} (${category.extend?.extendS2}) - 直接显示`);
			} else {
				// 如果原始配置中该分类的 isShow 不是 true，则重置为 false
				category.isShow = false;
				console.log(`  重置仓库分类 ${category.menuName} isShow 为 false`);
			}

			// 处理子菜单项的 isShow 状态
			if (category.childList) {
				console.log(`  处理仓库分类 ${category.menuName} 的 ${category.childList.length} 个子菜单项`);
				category.childList.forEach((childItem, childIndex) => {
					console.log(`    重置仓库子菜单 ${childIndex + 1}: ${childItem.menuName} (${childItem.extend?.extendS2})`);

					// 通过 extend.extendS2 查找原始配置中对应的项
					let originalIsShow = false;

					// 在所有原始分类中查找匹配的菜单项
					originalWarehouseMenuList.forEach(originalCategory => {
						if (originalCategory.childList) {
							originalCategory.childList.forEach(originalChild => {
								if (originalChild.extend?.extendS2 === childItem.extend?.extendS2) {
									originalIsShow = originalChild.isShow === true;
									console.log(`      找到原始仓库子菜单匹配: ${originalChild.menuName} (isShow: ${originalChild.isShow})`);
								}
							});
						}
					});

					// 如果原始配置中该项的 isShow 是 true，则保持 true，不做任何处理
					if (originalIsShow) {
						childItem.isShow = true; // 确保保持为 true
						console.log(`      ✓ 保留原本仓库子菜单显示状态: ${childItem.menuName} (${childItem.extend?.extendS2}) - 直接显示`);
					} else {
						// 如果原始配置中该项的 isShow 不是 true，则重置为 false
						childItem.isShow = false;
						console.log(`      重置仓库子菜单 ${childItem.menuName} isShow 为 false`);
					}
				});
			} else {
				console.log(`  仓库分类 ${category.menuName} 没有子菜单项`);
			}
		});

		console.log('仓库菜单 isShow 状态重置完成，原本为 true 的项已保留并直接显示');
	}

	/**
	 * 重置车间菜单的 isShow 状态，但保留原本为 true 的项
	 */
	resetWorkshopMenuShowButKeepOriginalTrue(workshopMenuList) {
		console.log('开始重置车间菜单 isShow 状态，保留原本为 true 的项');

		// 获取原始菜单配置
		const originalWorkshopMenuList = this.workshopMenuList || [];

		workshopMenuList.forEach((category, categoryIndex) => {
			console.log(`重置车间分类 ${categoryIndex + 1}: ${category.menuName} (${category.extend?.extendS2})`);

			// 处理分类级别的 isShow 状态
			let categoryOriginalIsShow = false;

			// 查找原始配置中对应的分类
			originalWorkshopMenuList.forEach(originalCategory => {
				if (originalCategory.extend?.extendS2 === category.extend?.extendS2) {
					categoryOriginalIsShow = originalCategory.isShow === true;
					console.log(`  找到原始车间分类匹配: ${originalCategory.menuName} (isShow: ${originalCategory.isShow})`);
				}
			});

			// 如果原始配置中该分类的 isShow 是 true，则保持 true，不做任何处理
			if (categoryOriginalIsShow) {
				category.isShow = true; // 确保保持为 true
				console.log(`  ✓ 保留原本车间分类显示状态: ${category.menuName} (${category.extend?.extendS2}) - 直接显示`);
			} else {
				// 如果原始配置中该分类的 isShow 不是 true，则重置为 false
				category.isShow = false;
				console.log(`  重置车间分类 ${category.menuName} isShow 为 false`);
			}

			// 处理子菜单项的 isShow 状态
			if (category.childList) {
				console.log(`  处理车间分类 ${category.menuName} 的 ${category.childList.length} 个子菜单项`);
				category.childList.forEach((childItem, childIndex) => {
					console.log(`    重置车间子菜单 ${childIndex + 1}: ${childItem.menuName} (${childItem.extend?.extendS2})`);

					// 通过 extend.extendS2 查找原始配置中对应的项
					let originalIsShow = false;

					// 在所有原始分类中查找匹配的菜单项
					originalWorkshopMenuList.forEach(originalCategory => {
						if (originalCategory.childList) {
							originalCategory.childList.forEach(originalChild => {
								if (originalChild.extend?.extendS2 === childItem.extend?.extendS2) {
									originalIsShow = originalChild.isShow === true;
									console.log(`      找到原始车间子菜单匹配: ${originalChild.menuName} (isShow: ${originalChild.isShow})`);
								}
							});
						}
					});

					// 如果原始配置中该项的 isShow 是 true，则保持 true，不做任何处理
					if (originalIsShow) {
						childItem.isShow = true; // 确保保持为 true
						console.log(`      ✓ 保留原本车间子菜单显示状态: ${childItem.menuName} (${childItem.extend?.extendS2}) - 直接显示`);
					} else {
						// 如果原始配置中该项的 isShow 不是 true，则重置为 false
						childItem.isShow = false;
						console.log(`      重置车间子菜单 ${childItem.menuName} isShow 为 false`);
					}
				});
			} else {
				console.log(`  车间分类 ${category.menuName} 没有子菜单项`);
			}
		});

		console.log('车间菜单 isShow 状态重置完成，原本为 true 的项已保留并直接显示');
	}

	/**
	 * 获取默认仓库配置
	 */
	getDefaultWarehouseConfig() {
		return this.warehouseMenuList.filter(item => item.isShow === true);
	}

	/**
	 * 获取默认车间配置
	 */
	getDefaultWorkshopConfig() {
		return this.workshopMenuList.filter(item => item.isShow === true);
	}

	/**
	 * 重置机床菜单的 isShow 状态，但保留原本为 true 的项
	 */
	resetMachineMenuShowButKeepOriginalTrue(machineMenuList) {
		console.log('开始重置机床菜单 isShow 状态，保留原本为 true 的项');

		// 获取原始菜单配置
		const originalMachineMenuList = this.machineMenuList || [];

		machineMenuList.forEach((item, index) => {
			console.log(`重置机床菜单项 ${index + 1}: ${item.menuName} (${item.extend?.extendS2})`);

			// 通过 extend.extendS2 查找原始配置中对应的项
			let originalIsShow = false;

			// 在原始配置中查找匹配的菜单项
			originalMachineMenuList.forEach(originalItem => {
				if (originalItem.extend?.extendS2 === item.extend?.extendS2) {
					originalIsShow = originalItem.isShow === true;
					console.log(`  找到原始机床菜单匹配: ${originalItem.menuName} (isShow: ${originalItem.isShow})`);
				}
			});

			// 如果原始配置中该项的 isShow 是 true，则保持 true，不做任何处理
			if (originalIsShow) {
				item.isShow = true; // 确保保持为 true
				console.log(`  ✓ 保留原本机床菜单显示状态: ${item.menuName} (${item.extend?.extendS2}) - 直接显示`);
			} else {
				// 如果原始配置中该项的 isShow 不是 true，则重置为 false
				item.isShow = false;
				console.log(`  重置机床菜单 ${item.menuName} isShow 为 false`);
			}
		});

		console.log('机床菜单 isShow 状态重置完成，原本为 true 的项已保留并直接显示');
	}

	/**
	 * 获取默认机床配置
	 */
	getDefaultMachineConfig() {
		return this.machineMenuList.filter(item => item.isShow === true);
	}

	/**
	 * 测试仓库菜单匹配（用于调试）
	 */
	testWarehouseMenuMatch() {
		console.log('=== 测试仓库菜单匹配 ===');

		// 模拟 API 返回数据
		const testApiData = [
			{
				menuName: '存放',
				extend: { extendS2: 'meun.m8.ck.cunfang' }
			},
			{
				menuName: '取出',
				extend: { extendS2: 'meun.m8.ck.quchu' }
			}
		];

		console.log('测试 API 数据:', testApiData);

		// 执行匹配
		const result = this.matchWarehouseMenus(testApiData);

		console.log('测试匹配结果:', result);
		console.log('=== 测试完成 ===');

		return result;
	}

	/**
	 * 清除本地存储的菜单配置
	 */
	clearLocalStorage() {
		try {
			uni.removeStorageSync('matchedTabBarMenus');
			uni.removeStorageSync('matchedProcessMenus');
			uni.removeStorageSync('matchedWarehouseMenus');
			uni.removeStorageSync('matchedWorkshopMenus');
			uni.removeStorageSync('matchedMachineMenus');
			uni.removeStorageSync('menuMatchTimestamp');
			console.log('✓ 本地菜单存储已清除');
		} catch (error) {
			console.error('清除本地菜单存储失败:', error);
		}
	}
}

// 创建单例实例
const menuMatcher = new MenuMatcher();

export default menuMatcher;
