/**
 * 防重复提交和防双击 Mixin
 * 使用方法：
 * 1. 在组件中引入：import preventDuplicateSubmit from '@/mixins/preventDuplicateSubmit.js'
 * 2. 在 mixins 中使用：mixins: [preventDuplicateSubmit]
 * 3. 在提交方法中调用：if (this.preventDuplicateClick()) return;
 * 4. 在提交开始时调用：this.setSubmitting(true)
 * 5. 在提交结束时调用：this.setSubmitting(false)
 */

export default {
	data() {
		return {
			isSubmitting: false, // 防止重复提交标志位
			lastClickTime: 0, // 记录上次点击时间，防止双击
			clickInterval: 500, // 防双击间隔时间（毫秒）
		}
	},
	
	methods: {
		/**
		 * 防止重复点击检查
		 * @param {number} interval 防双击间隔时间，默认使用 this.clickInterval
		 * @returns {boolean} true表示应该阻止执行，false表示可以继续执行
		 */
		preventDuplicateClick(interval = this.clickInterval) {
			const currentTime = Date.now();
			
			// 防止双击（指定时间内的重复点击）
			if (currentTime - this.lastClickTime < interval) {
				console.log('点击过快，请稍后再试');
				return true;
			}
			this.lastClickTime = currentTime;
			
			// 防止重复提交
			if (this.isSubmitting) {
				console.log('正在提交中，请勿重复点击');
				return true;
			}
			
			return false;
		},
		
		/**
		 * 设置提交状态
		 * @param {boolean} status 提交状态
		 */
		setSubmitting(status) {
			this.isSubmitting = status;
		},
		
		/**
		 * 重置防重复提交状态
		 */
		resetSubmitState() {
			this.isSubmitting = false;
			this.lastClickTime = 0;
		},
		
		/**
		 * 包装提交方法，自动处理防重复提交逻辑
		 * @param {Function} submitFn 实际的提交函数
		 * @param {number} interval 防双击间隔时间
		 * @returns {Promise} 提交结果
		 */
		async wrapSubmit(submitFn, interval = this.clickInterval) {
			// 防重复检查
			if (this.preventDuplicateClick(interval)) {
				return Promise.reject('重复提交');
			}
			
			// 设置提交状态
			this.setSubmitting(true);
			
			try {
				// 执行提交函数
				const result = await submitFn();
				return result;
			} catch (error) {
				console.error('提交失败:', error);
				throw error;
			} finally {
				// 重置提交状态
				this.setSubmitting(false);
			}
		}
	},
	
	/**
	 * 组件销毁时重置状态
	 */
	beforeDestroy() {
		this.resetSubmitState();
	}
}
