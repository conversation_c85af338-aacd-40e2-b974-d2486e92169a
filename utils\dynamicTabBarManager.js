/**
 * 动态 TabBar 管理器
 * 用于处理 API 数据并转换为 TabBar 配置
 */

// 导入菜单配置
import menuConfig from '@/menu.json';

// 从 menu.json 获取 TabBar 配置池
const defaultTabBarPool = menuConfig.tabMenuList || [];

// 固定的"我的" TabBar 配置
const myTabBarConfig = {
	text: "我的",
	pagePath: "pages/sys/index/user/index",
	iconPath: "/static/image/zfgs/my.png",
	selectedIconPath: "/static/image/zfgs/my_select.png",
	extend: { extendS2: "menu:my" }
};

/**
 * 动态 TabBar 管理器类
 */
class DynamicTabBarManager {
	constructor() {
		this.currentConfig = [];
	}

	/**
	 * 构建动态 TabBar 配置
	 * @param {Array} apiMenuItems - API 返回的菜单项
	 * @returns {Array} 构建好的 TabBar 配置
	 */
	buildDynamicTabBarConfig(apiMenuItems) {
		console.log('开始构建动态 TabBar 配置...');
		console.log('API 菜单项数量:', apiMenuItems?.length || 0);

		const result = [];

		// 处理 API 菜单项
		if (apiMenuItems && apiMenuItems.length > 0) {
			apiMenuItems.forEach((apiItem, index) => {
				console.log(`处理 API 菜单项 ${index + 1}: ${apiItem.menuName} ${apiItem.extend?.extendS2}`);

				// 在配置池中查找匹配的配置
				const matchedConfig = defaultTabBarPool.find(poolItem => {
					const isMatch = poolItem.extend.extendS2 === apiItem.extend?.extendS2;
					console.log(`  检查配置池项 "${poolItem.text}": ${poolItem.extend.extendS2} === ${apiItem.extend?.extendS2} = ${isMatch}`);
					return isMatch;
				});

				if (matchedConfig) {
					console.log(`  ✓ 找到匹配配置: ${matchedConfig.text}`);
					
					// 创建 TabBar 项目，优先使用 API 数据，配置池数据作为补充
					const tabBarItem = {
						text: apiItem.menuName || matchedConfig.text,
						pagePath: this.normalizePagePath(apiItem.menuUrl || matchedConfig.pagePath),
						iconPath: matchedConfig.iconPath,
						selectedIconPath: matchedConfig.selectedIconPath,
						extend: apiItem.extend || matchedConfig.extend
					};

					console.log(`  添加到结果: ${tabBarItem.text} (${tabBarItem.pagePath})`);
					result.push(tabBarItem);
				} else {
					console.log(`  ✗ 未找到匹配配置，跳过: ${apiItem.menuName}`);
				}
			});
		}

		// 添加固定的"我的" TabBar 到最后位置
		console.log('添加固定的"我的" TabBar 到最后位置');
		result.push(myTabBarConfig);

		console.log('动态 TabBar 配置构建完成，总数量:', result.length);
		result.forEach((item, index) => {
			console.log(`  ${index}: ${item.text} (${item.pagePath})`);
		});

		return result;
	}

	/**
	 * 标准化页面路径
	 * @param {String} pagePath - 页面路径
	 * @returns {String} 标准化后的路径
	 */
	normalizePagePath(pagePath) {
		if (!pagePath) return '';
		
		// 移除开头的斜杠
		let normalized = pagePath.startsWith('/') ? pagePath.substring(1) : pagePath;
		
		// 确保路径格式正确
		if (!normalized.startsWith('pages/')) {
			normalized = `pages/${normalized}`;
		}
		
		return normalized;
	}

	/**
	 * 验证 TabBar 配置
	 * @param {Array} config - TabBar 配置
	 * @returns {Boolean} 是否有效
	 */
	validateTabBarConfig(config) {
		if (!Array.isArray(config) || config.length === 0) {
			console.warn('TabBar 配置无效: 不是数组或为空');
			return false;
		}

		if (config.length > 5) {
			console.warn('TabBar 配置无效: 项目数量超过5个');
			return false;
		}

		const isValid = config.every((item, index) => {
			const hasRequiredFields = item.text && item.pagePath && item.iconPath && item.selectedIconPath;
			if (!hasRequiredFields) {
				console.warn(`TabBar 项目 ${index} 缺少必需字段:`, item);
				return false;
			}
			return true;
		});

		if (isValid) {
			console.log('TabBar 配置验证通过');
		}

		return isValid;
	}

	/**
	 * 更新动态 TabBar
	 * @param {Array} apiMenuItems - API 返回的菜单项
	 * @returns {Array} 更新后的 TabBar 配置
	 */
	updateDynamicTabBar(apiMenuItems) {
		console.log('=== 开始动态更新 TabBar ===');

		// 构建动态配置
		const dynamicConfig = this.buildDynamicTabBarConfig(apiMenuItems);

		// 验证配置
		if (!this.validateTabBarConfig(dynamicConfig)) {
			console.warn('使用默认 TabBar 配置');
			return this.getDefaultConfig();
		}

		// 更新当前配置
		this.currentConfig = dynamicConfig;

		console.log('=== TabBar 动态更新完成 ===');
		return dynamicConfig;
	}

	/**
	 * 获取默认配置
	 * @returns {Array} 默认 TabBar 配置
	 */
	getDefaultConfig() {
		// 从 menu.json 中获取 isShow 为 true 的菜单项
		const visibleMenus = defaultTabBarPool.filter(item => item.isShow === true);

		// 如果没有可见菜单，返回"我的"菜单作为备用
		if (visibleMenus.length === 0) {
			return [myTabBarConfig];
		}

		return visibleMenus;
	}

	/**
	 * 获取当前配置
	 * @returns {Array} 当前 TabBar 配置
	 */
	getCurrentConfig() {
		return this.currentConfig;
	}

	/**
	 * 重置配置
	 */
	reset() {
		this.currentConfig = [];
		console.log('动态 TabBar 管理器已重置');
	}
}

// 创建单例实例
const dynamicTabBarManager = new DynamicTabBarManager();

export default dynamicTabBarManager;

// 导出常用方法
export const {
	buildDynamicTabBarConfig,
	updateDynamicTabBar,
	validateTabBarConfig,
	getCurrentConfig,
	getDefaultConfig,
	reset
} = dynamicTabBarManager;
