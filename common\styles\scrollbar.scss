/**
 * 滚动条样式工具类
 * 用于隐藏滚动条但保持滚动功能
 */

/* 隐藏滚动条的 mixin */
@mixin hide-scrollbar {
	/* 隐藏滚动条但保持滚动功能 */
	scrollbar-width: none; /* Firefox */
	-ms-overflow-style: none; /* IE 和 Edge */

	/* 当内容不足时不显示滚动条 */
	&::-webkit-scrollbar {
		display: none; /* Chrome, Safari, Opera */
	}
}

/* 隐藏滚动条的通用类 */
.hide-scrollbar {
	@include hide-scrollbar;
}

/* 垂直滚动容器 */
.scroll-container-y {
	overflow-y: auto;
	overflow-x: hidden;
	@include hide-scrollbar;
}

/* 水平滚动容器 */
.scroll-container-x {
	overflow-x: auto;
	overflow-y: hidden;
	@include hide-scrollbar;
}

/* 双向滚动容器 */
.scroll-container-both {
	overflow: auto;
	@include hide-scrollbar;
}

/* 自定义滚动条样式（可选，用于需要显示滚动条的场景） */
.custom-scrollbar {
	/* 滚动条整体样式 */
	&::-webkit-scrollbar {
		width: 6px;
		height: 6px;
	}

	/* 滚动条轨道 */
	&::-webkit-scrollbar-track {
		background: #f1f1f1;
		border-radius: 3px;
	}

	/* 滚动条滑块 */
	&::-webkit-scrollbar-thumb {
		background: #c1c1c1;
		border-radius: 3px;

		&:hover {
			background: #a8a8a8;
		}
	}

	/* 滚动条角落 */
	&::-webkit-scrollbar-corner {
		background: #f1f1f1;
	}
}

/* 页面内容容器样式 */
.page-content-container {
	flex: 1;
	overflow-y: auto;
	overflow-x: hidden;
	position: relative;
	@include hide-scrollbar;
}

/* scroll-view 组件的隐藏滚动条样式 */
.scroll-view-hide-bar {
	@include hide-scrollbar;
}
