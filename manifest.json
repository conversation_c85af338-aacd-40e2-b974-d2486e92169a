
/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 */
{
    "name" : "机加执行管理系统（M8）",
    "appid" : "__UNI__6A43D7F",
    "description" : "重庆轻企信息技术有限公司",
    "versionName" : "1.0.1",
    "versionCode" : "100",
    "transformPx" : false,
    "app-plus" : {
        "compatible" : {
            "ignoreVersion" : true
        },
        // APP-VUE分包，可提APP升启动速度，2.7.12开始支持，兼容微信小程序分包方案，默认关闭
        "optimization" : {
            "subPackages" : true
        },
        "safearea" : {
            "bottom" : {
                "offset" : "none"
            }
        },
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        "usingComponents" : true,
        "nvueCompiler" : "uni-app",
        "compilerVersion" : 3,
        "modules" : {
            "Barcode" : {},
            "Bluetooth" : {},
            "Camera" : {}
        },
        "distribute" : {
            "android" : {
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_CHECKIN_PROPERTIES\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.EXPAND_STATUS_BAR\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.MEDIA_CONTENT_CONTROL\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_FORMAT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_CALL_LOG\"/>",
                    "<uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-feature android:name=\"android.hardware.camera\"/>"
                ],
                "abiFilters" : [ "armeabi-v7a", "arm64-v8a" ],
                "minSdkVersion" : 22
            },
            "ios" : {
                "dSYMs" : false
            },
            "sdkConfigs" : {
                "ad" : {},
                "oauth" : {}
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            },
            "splashscreen" : {
                "androidStyle" : "common"
            }
        },
        "nativePlugins" : {}
    },
    "quickapp" : {},
    "mp-weixin" : {
        "appid" : "wxb6cd931ce721e462",
        "setting" : {
            "urlCheck" : false,
            "es6" : true,
            "minified" : true,
            "postcss" : true
        },
        "optimization" : {
            "subPackages" : true
        },
        "permission" : {
            "scope.writePhotosAlbum" : {
                "desc" : "需要保存图片到相册权限"
            },
            "scope.readWritePhotosAlbum" : {
                "desc" : "需要读写相册权限"
            }
        },
        "usingComponents" : true,
        "lazyCodeLoading" : "requiredComponents"
    },
    "mp-alipay" : {
        "usingComponents" : true,
        "component2" : true
    },
    "mp-qq" : {
        "optimization" : {
            "subPackages" : true
        },
        "appid" : ""
    },
    "mp-baidu" : {
        "usingComponents" : true,
        "appid" : ""
    },
    "mp-toutiao" : {
        "usingComponents" : true,
        "appid" : ""
    },
    "h5" : {
        "template" : "h5.html",
        "router" : {
            "mode" : "hash",
            "base" : "/app"
        },
        "optimization" : {
            "treeShaking" : {
                "enable" : false
            }
        },
        "title" : "JeeSite",
        "domain" : "/app"
    }
}
