<template>
  <view class="wrap">
    <js-lang title="user.title"></js-lang>
    <view class="header" >
      <view class="userinfo">
        <view class="image" @click="navTo('info')"
          ><image :src="avatarUrl"></image
        ></view>
        <view class="info">
          <view class="username">{{
            vuex_user.userName || $t("login.noLogin")
          }}</view>
          <view class="usercode">{{
            vuex_user.loginCode || $t("login.noLogin")
          }}</view>
        </view>
      </view>
	  <!-- {{
          $t("login.logoutButton")
        }} -->
      <view class="logout"
        ><u-button type="success" shape="circle" size="mini" @click="logout">
			重新登录
		</u-button></view
      >
    </view>
    <view class="u-p-t-10 u-p-b-20">
      <view class="u-m-t-20">
        <u-cell-group>
          <u-cell-item icon="account" :iconSize="iconSize" :iconStyle="{color:'#266bff'}"
						title="个人信息" @click="navTo('info')"></u-cell-item>
          <u-cell-item
            icon="lock"
            :iconSize="iconSize"
            :iconStyle="{ color: '#1bca6a' }"
            title="修改密码"
            @click="navTo('pwd')"
          ></u-cell-item>
          <u-cell-item
            icon="heart"
            :iconSize="iconSize"
            :iconStyle="{ color: '#d99e59' }"
            title="关于我们"
            @click="navTo('about')"
          ></u-cell-item>
		  <!-- <u-cell-item icon="account" :iconSize="iconSize" :iconStyle="{color:'#266bff'}" title="用户服务协议" @click="navTo('agreement')"></u-cell-item>
		  <u-cell-item icon="account" :iconSize="iconSize" :iconStyle="{color:'#266bff'}" title="隐私政策" @click="navTo('privacy')"></u-cell-item>
          <u-cell-item icon="setting" :iconSize="iconSize" :iconStyle="{ color: '#d99e59' }"
						title="蓝牙打印机设置" @click="navTo('print')"></u-cell-item> -->
						
		  
		  <!-- <view class="list-call" v-if="environment != 'wxwork'">
			<image class="u-icon-right" :src="'/static/jeesite/login/eye_' + (showPassword ? 'open' : 'close') + '.png'" @click="showPass()"></image>
		  	<input class="u-input" disabled type="text" v-model="vuex_user.wxOpenid" maxlength="32" :placeholder="' '" :password="!showPassword" />
			<view  @click="Unbinding"
			style="margin-left: 16rpx;height: 35px;width: 60px;text-align: center;line-height: 35px;background-color: #27aa03;color: #fff;border-radius: 5px;"  
			>解绑</view>
		  </view> -->
        </u-cell-group>
      </view>
    </view>
    <view style="height: 1px"></view>
    <view style="height: 108rpx"></view>
   <!-- <custom-tab-bar ref="customtab1" :current-index="currentIndex"  @changeTab="handleTabChange" v-if="customTabBar" /> -->
  </view>
</template>
<script>
// import {hasPermission} from '@/common/fire.js'
import customTabBar from '@/pages/m8/componetns/customTabBar.vue';
import logoutHelper from '@/utils/logoutHelper.js';

export default {
  components: { customTabBar },
  data() {
    return {
      iconSize: 38,
      currentIndex: 3,
	  customTabBar:true,
	  // flag:hasPermission('proj:attachment:pmAttachment:down'),
	  environment:'',
	  showPassword: false,
    };
  },
  onShow() {
    this.customTabBar = false ;
    setTimeout(()=>{
        this.customTabBar = true ;
    },100)
	let res1 = wx.getSystemInfoSync()
	this.environment = res1.environment
  },
  computed: {
    avatarUrl() {
      let url = this.vuex_user.avatarUrl || "/ctxPath/static/images/user1.jpg";
      url = url.replace("/ctxPath/", this.vuex_config.baseUrl + "/");
      return url + "?t=" + new Date().getTime();
    },
  },
  methods: {
    navTo(url) {
      console.log('/pagesData/user/'+url, "navTo"); 
      uni.navigateTo({
        url: '/pagesData/user/'+url,
      });
    },
    handleTabChange(pagePath) {
      // 切换页面逻辑，可使用uni.navigateTo等API
      console.log('pagePath',pagePath)
      uni.reLaunch({ url: pagePath });
    },
	showPass() {
		this.showPassword = !this.showPassword;
	},
    Unbinding() {
      this.$u.api
        .cleanWxOpenid({
          userCode: this.vuex_user.userCode,
        })
        .then((res) => {
          console.log(res, "===");
          this.out();
        });
    },
    out() {
      // 解绑微信后退出登录
      logoutHelper.logout({
        callLogoutAPI: true,
        showToast: true,
        redirectUrl: "/pages/sys/index/login/index",
        delay: 500
      });
    },
    async logout() {
      // 确认退出登录
      uni.showModal({
        title: '提示',
        content: '确定要退出登录吗？',
        showCancel: true,
        success: async (res) => {
          if (res.confirm) {
            // 使用新的退出登录工具
            await logoutHelper.logout({
              callLogoutAPI: true,
              showToast: true,
              redirectUrl: "/pages/sys/index/login/index",
              delay: 500
            });
          }
        }
      });
    },
    upgrade() {
      // #ifdef APP-PLUS
      this.$u.api.upgradeCheck().then((res) => {
        if (res.result == "true") {
          uni.showModal({
            title: "提示",
            content: res.message + "是否下载更新？",
            showCancel: true,
            success: function (res2) {
              if (res2.confirm) {
                plus.runtime.openURL(res.data.apkUrl);
              }
            },
          });
        } else {
          this.$u.toast(res.message);
        }
      });
      // #endif
      // #ifndef APP-PLUS
      this.$u.toast("小程序端或H5端无需检查更新");
      // #endif
    },
  },
};
</script>
<style lang="scss">
@import "index.scss";
page {
  background-color: #f8f8f8;
}


.list-call {
	padding: 0 32rpx;
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	align-items: center;
	height: 120rpx;
	// padding-top: 10rpx;
	// font-weight: normal; 
	// color: #333333;
	// border-bottom: 0.5px solid #e2e2e2;
}

.list-call .u-input {
	flex: 1;
	// font-size: 39rpx;
	text-align: left;
	margin-left: 16rpx;
}

.list-call .u-icon-right {
	color: #aaaaaa;
	width: 38rpx;
	height: 38rpx;
}
</style>
