<template>
	<view class="wrap ">
		<js-error mode="bottom" ref="jsError"></js-error>
		<xw-scan></xw-scan>
		
		<u-popup v-model="show" mode="center" :mask-close-able="false" width="80%" border-radius="10">
			<view style="width: 100%;height: 100%;">
				<view
					style="width: 100%;text-align: center;padding: 10px;font-size: 18px;background: #3E97B0;color: #fff;">
					{{barCodeTitle}}
				</view>
				<view style="width: 100%;padding: 10px;">
					<u-input type="textarea" ref="inputRefComponent" :clearable="false" placeholder="请扫描图纸和工牌" v-model="inputRef" height="400"
						maxlength="500" :focus="isInputFocused"/>
				</view>
				<view style="color: red;padding: 0 10px;min-height: 20px">
					{{tips}}
				</view>
				<view style="padding: 20rpx 40rpx;">
					<view style=" display: flex;justify-content: space-between;margin-bottom: 20rpx; ">
						<u-button style="flex: 1;margin-right: 20rpx;" plain class="btn" type="primary" @click="clearText('1')">重新扫描</u-button>
						<u-button style="flex: 1;" plain class="btn" type="primary" @click="search">扫一扫</u-button>
					</view>
					<view style=" display: flex;justify-content: space-between; ">
						<u-button style="flex: 1;margin-right: 20rpx;" type="error" plain class="btn" @click="cancel">关闭</u-button>
						<u-button style="flex: 1;" plain class="btn" type="success" @click="endScan('do')">结束扫描</u-button>
					</view>
				</view>
			</view>
		</u-popup>
		
	</view>
	
</template>
<script>
	export default {
		data() {
			return {
				barCodeTitle: '数铣送检',
				queryData: {
					funcType: '',
				},
				tips: '',
				barCode : '',
				show: false,
				inputRef: '',
				isInputFocused: false,
				enterCount: 0,
			}
			
		},
		onShow() {
			console.log(123)
			// 开启广播监听事件
			uni.$on('xwscan', this.BroadcastScanningToObtainData)
			// this.query.pageNo = 1;
			// this.loadData();
		
		},
		onHide() {
			// console.log('页面销毁（onUnload）广播监听事件：xwscan')
			// 销毁广播监听事件
			uni.$off('xwscan', this.BroadcastScanningToObtainData)
		},
		onUnload() {
			// console.log('页面销毁（onUnload）广播监听事件：xwscan')
			// 销毁广播监听事件
			uni.$off('xwscan', this.BroadcastScanningToObtainData)
		},
		onLoad(e) {
			
			
		},
		methods: {
			// 初始化广播监听的方法
			initBroadcastListener() {
				console.log('初始化广播监听 xwscan');
				// 开启广播监听事件
				uni.$on('xwscan', this.BroadcastScanningToObtainData);
			},
			sendMp3(name) {
				console.log("=====testClick=====");
				let src = '/static/jeesite/' + name + '.mp3';
				//实例化声音  
				const Audio = uni.createInnerAudioContext();
				Audio.autoplay = true;
				Audio.src = src; //音频地址  
				Audio.play(); //执行播放  
				Audio.onError((res) => {
					console.log(res.errMsg);
					console.log(res.errCode);
				});
				Audio.onPause(function() {
					console.log('end');
					Audio.destroy();
				});
			},
			showPopup() {
				this.show = true;
				console.log(this.vuex_config.checkMachineTypeEnum.repCheck)
				this.queryData.funcType = this.vuex_config.checkMachineTypeEnum.repCheck;
				this.initBroadcastListener();


				if(this.vuex_config.deviceType === this.vuex_config.deviceTypePDA){
					this.$nextTick(() => {
                    this.isInputFocused = false; // 控制输入框聚焦
                    // 短暂延迟后取消聚焦状态，避免后续问题
                    setTimeout(() => {
							// this.isInputFocused = true;
						}, 100);
					});
				}
 				
			},
			cancel(){
				this.show = false;
				this.inputRef = '';
				this.clearText('1');
			},
			endScan(type) {
				console.log('type',type)
				let that = this
				let lines = this.inputRef.split(/[(\r\n)\r\n]+/);
				lines.forEach((item, index) => {
					// 删除空项
					if (item === '') {
						lines.splice(index, 1);
					}
				});
				this.enterCount++;
				lines.forEach((item) => {
					if (item.startsWith('98-')) {
						this.queryData.barUser = item;
					} else if (item.startsWith('99-')) {
						// this.queryData.barPic = item;
						
					} 
				});
				
				let picArr = lines.filter((item) => {
				      return item.startsWith('99-');
				});
				
				
				console.log('lines',lines,this.queryData.barPic)
				// this.vuex_config.deviceType === this.vuex_config.deviceTypeAPP
				if(!this.vuex_config.workScan){
					if(this.enterCount == '1' || type == 'do'){
						if (picArr < 1) {
						    this.tips = '图纸条码不正确!';
						    // this.clearText();
							// this.enterCount = 0;
							this.sendMp3('sb');
							return false;
						}
						// if (!this.queryData.barUser) {
						//     this.tips = '用户条码不正确!';
						// 	this.sendMp3('sb');
						// 	return false;
						// }
						this.$u.api.m8.checkMachineBar({
							...this.queryData,
							barUser:'98-' + this.vuex_user.userCode
						}).then((res) => {
							console.log(!res.data.errorMsg,'!res.data.errorMsg')
							if(res.result == 'true' && !res.data.errorMsg){
								this.sendMp3('cg');
								this.show = false;
								this.inputRef = '';
								
								uni.navigateTo({
									url: '/pages/m8/repCheck/list?params='+JSON.stringify(res.data.repCheck),
								});
								this.clearText('1');
							}else{
								this.sendMp3('sb');
								this.tips = res.message || res.data.errorMsg
							}
						});
					}
				}else{
					if(this.enterCount == '2' || type == 'do'){
						if ( picArr < 1 ) {
						    this.tips = '图纸条码不正确!';
						    // this.clearText();
							// this.enterCount = 0;
							this.sendMp3('sb');
							return false;
						}
						if (!this.queryData.barUser) {
						    this.tips = '用户条码不正确!';
						    // this.clearText();
							// this.enterCount = 0;
							this.sendMp3('sb');
							return false;
						}
						console.log(this.queryData,'this.queryData')
						this.queryData.barPic = picArr.join(',')
						this.$u.api.m8.checkMachineBar(this.queryData).then((res) => {
							if(res.result == 'true' && !res.data.errorMsg){
								this.sendMp3('cg');

								
								this.show = false;
								uni.navigateTo({
									url: '/pages/m8/repCheck/list?params='+JSON.stringify(res.data.repCheck),
								});
								this.clearText('1');
							}else{
								this.sendMp3('sb');
								this.tips = res.message || res.data.errorMsg
							}
						});
					}
				}
				
				
			},
			clearText(flag) {
				this.inputRef = '';
				this.enterCount = 0;
				this.queryData.barPic = ''
				this.queryData.barUser = '';
				this.barCode = ''
				uni.$off('xwscan', this.BroadcastScanningToObtainData);
				if(flag == '1'){
					this.tips = ''
				}
			},
			BroadcastScanningToObtainData(res) {
				console.log('res',res)
				//获取扫描到的条形码
				let barcode = res.code
			
				//判断条形码长度是否大于3
				if (barcode.length > 3) {
					//去除换行符
					let newString = barcode.replace('\n;', '');
			
					this.barCode = newString + '\n';;
					
					this.confirm()
				}
			},
			confirm() {
				let _this = this;
				this.inputRef += this.barCode
				console.log('this.inputRef',this.inputRef)
				
				this.endScan()
				setTimeout(() => {
					this.barCode = ''
				}, 500)
			},
			search() {
				let _that = this;
				if(!this.show) {
					this.show = true;
					this.queryData.funcType = this.vuex_config.checkMachineTypeEnum.repCheck;
					if(this.vuex_config.deviceType === this.vuex_config.deviceTypePDA){
						this.$nextTick(() => {
						this.isInputFocused = false; // 控制输入框聚焦
						// 短暂延迟后取消聚焦状态，避免后续问题
						setTimeout(() => {
								this.isInputFocused = true;
							}, 100);
						});
					} else {
						this.initBroadcastListener();
					}
				}
				uni.scanCode({
					scanType: ["barCode", "qrCode"],
					// onlyFromCamera: true,
					success: function(res) {
						console.log("条码类型：" + res.scanType);
						console.log("条码内容：" + res.result);
						_that.barCode = res.result + '\n';
						_that.confirm()
						if(_that.vuex_config.workScan && _that.enterCount <2){
							setTimeout(()=>{
								_that.search()
							},500)
						}
					},
				});
			},
		}
	}
	
</script>

<style lang="scss" scoped>
	
	
</style>