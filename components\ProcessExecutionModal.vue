<template>
	<view>
		<js-error mode="bottom" ref="jsError"></js-error>
		<xw-scan></xw-scan>
		<u-popup v-model="show" mode="center" :mask-close-able="false" width="80%" border-radius="10">
			<view class="modal-container" style="width: 100%;height: 100%;">
				<!-- 与 machine/index.vue 完全一致的弹窗内容 style="padding: 20rpx 40rpx;" -->
				<view >
					<view style="width: 100%;text-align: center;padding: 10px;font-size: 18px;background: #3E97B0;color: #fff;">
						{{ barCodeTitle }}
					</view>

					<view style="width: 100%;padding: 10px;">
						<u-input
							v-model="inputRef"
							type="textarea"
							:auto-height="true"
							placeholder="请扫描图纸和工牌"
							:focus="isInputFocused"
							height="400"
							maxlength="500"
						/>
					</view>

					<view style="color: red;padding: 0 10px;min-height: 20px">
						{{ tips }}
					</view>

					<view style="padding: 20rpx 40rpx;">
						<view style=" display: flex;justify-content: space-between;margin-bottom: 20rpx; ">
							<u-button style="flex: 1;margin-right: 20rpx;" plain class="btn" type="primary" @click.stop="clearText('1')">重新扫描</u-button>
							<u-button style="flex: 1;" plain class="btn" type="primary" @click.stop="search">扫一扫</u-button>
						</view>
						<view style=" display: flex;justify-content: space-between; ">
							<u-button style="flex: 1;margin-right: 20rpx;" type="error" plain class="btn" @click.stop="cancel">关闭</u-button>
							<u-button style="flex: 1;" plain class="btn" type="success" @click.stop="endScan('do')">结束扫描</u-button>
						</view>
					</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import { mapState } from 'vuex';

	export default {
		name: 'ProcessExecutionModal',
		props: {
			value: {
				type: Boolean,
				default: false
			},
			menuItem: {
				type: Object,
				default: () => ({})
			}
		},
		onShow() {
			// 开启广播监听事件
			uni.$on('xwscan', this.BroadcastScanningToObtainData)		
		},
		onHide() {
			// console.log('页面销毁（onUnload）广播监听事件：xwscan')
			// 销毁广播监听事件
			uni.$off('xwscan', this.BroadcastScanningToObtainData)
		},
		onUnload() {
			// console.log('页面销毁（onUnload）广播监听事件：xwscan')
			// 销毁广播监听事件
			uni.$off('xwscan', this.BroadcastScanningToObtainData)
		},
		data() {
			return {
				show: false,
				tips: '',
				checkData: {},
				queryData: {
					funcType: '',
					barUser: '',
					barPic: ''
				},
				barCodeTitle: '',
				barCode: '',
				inputRef: '',
				enterCount: 0,
				model: {},
				isInputFocused: false,
				isProcessing: false, // 防止重复处理扫码结果
				isClosing: false, // 防止重复关闭弹窗
			};
		},
		computed: {
			...mapState(['vuex_user', 'vuex_config'])
		},
		watch: {
			value(newVal) {
				this.show = newVal;
				if (newVal) {
					this.openModal();
				}
			},
			show(newVal) {
				this.$emit('input', newVal);
			},
			inputRef(newVal) {
				// 监听输入变化，但不自动触发广播处理，避免重复调用
				console.log('inputRef 变化:', newVal);
			},
			menuItem: {
				handler(newVal) {
					// 当 menuItem 发生变化时，重新设置标题
					if (newVal && Object.keys(newVal).length > 0) {
						console.log('menuItem 更新:', newVal);
						this.setModalTitle();
					}
				},
				deep: true,
				immediate: true
			}
		},
		methods: {

			showPopup() {
				this.show = true;
				this.initBroadcastListener();
				this.openModal();

				if(this.vuex_config.deviceType === this.vuex_config.deviceTypePDA){
					this.$nextTick(() => {
                    this.isInputFocused = false; // 控制输入框聚焦1
                    // 短暂延迟后取消聚焦状态，避免后续问题
                    setTimeout(() => {
							// this.isInputFocused = true;
						}, 100);
					});
				}
 				
			},
			// 初始化广播监听的方法
			initBroadcastListener() {
				console.log('初始化广播监听 xwscan');
				// 开启广播监听事件
				uni.$on('xwscan', this.BroadcastScanningToObtainData);
			},
			/** 发生声音*/
			sendMp3(name) {
				console.log("=====testClick=====");
				let src = '/static/jeesite/' + name + '.mp3';
				//实例化声音  
				const Audio = uni.createInnerAudioContext();
				Audio.autoplay = true;
				Audio.src = src; //音频地址  
				Audio.play(); //执行播放  
				Audio.onError((res) => {
					console.log(res.errMsg);
					console.log(res.errCode);
				});
				Audio.onPause(function() {
					console.log('end');
					Audio.destroy();
				});
			},

			/**
			 * 打开弹窗
			 */
			openModal() {
				console.log('打开工序执行弹窗:', this.menuItem);
				this.setModalTitle();
				this.initializeData();
			},

			/**
			 * 关闭弹窗
			 */
			cancel() {
				console.log('cancel 方法被调用，当前 show 状态:', this.show, '关闭状态:', this.isClosing);

				// 防止重复关闭
				if (this.isClosing) {
					console.log('正在关闭中，跳过重复操作');
					return;
				}

				this.isClosing = true;

				// 强制关闭弹窗
				this.show = false;
				this.clearData();
				this.$emit('input', false);

				// 重置关闭状态
				setTimeout(() => {
					this.isClosing = false;
				}, 100);

				console.log('弹窗已强制关闭');
			},

			/**
			 * 设置弹窗标题和功能类型
			 */
			setModalTitle() {
				const extendS2 = this.menuItem?.extend?.extendS2;
				switch (extendS2) {
					case 'menu:m8:gx:kaigong':
						this.barCodeTitle = '开工';
						this.queryData.funcType = this.vuex_config.flowTypeEnum.beginWork;
						break;
					case 'menu:m8:gx:wangong':
						this.barCodeTitle = '完工';
						this.queryData.funcType = this.vuex_config.flowTypeEnum.endWork;
						break;
					case 'menu:m8:gx:songjian':
						this.barCodeTitle = '送检';
						this.queryData.funcType = this.vuex_config.flowTypeEnum.repCheck;
						break;
					default:
						this.barCodeTitle = this.menuItem?.menuName || '工序执行';
						this.queryData.funcType = this.vuex_config.flowTypeEnum.beginWork;
				}
			},

			/**
			 * 初始化数据
			 */
			initializeData() {
				this.setModalTitle();
				this.enterCount = 0;
				this.tips = '';
				this.inputRef = '';
				// this.getData();
			},

			/**
			 * 清除数据
			 */
			clearData() {
				this.tips = '';
				this.inputRef = '';
				this.barCode = '';
				this.enterCount = 0;
				this.isProcessing = false; // 重置处理标志
				this.isClosing = false; // 重置关闭标志
				this.queryData = {
					funcType: '',
					barUser: '',
					barPic: ''
				};
				// 移除事件监听器
				uni.$off('xwscan', this.BroadcastScanningToObtainData);
			},
			
			/**
			 * 获取机床数据（与 machine/index.vue 的 getData 方法完全一致）
			 */
			// getData(type) {
			// 	console.log(this.vuex_user.userCode, '====');
			// 	let that = this;
			// 	this.$u.api.m8.machineListData({ id: this.queryData.machineId }).then((res) => {
			// 		that.model = res.list[0];
			// 		if (type == 'reload') {
			// 			that.$u.toast('刷新成功！');
			// 		}
			// 	});
			// },

			/**
			 * 清除文本（与 machine/index.vue 的 clearText 方法一致）
			 */
			clearText(type) {
				console.log('clearText 方法被调用，type:', type);

				this.inputRef = '';
				this.barCode = '';
				this.tips = '';
				this.enterCount = 0;
				this.queryData.barUser = '';
				this.queryData.barPic = '';
				this.isProcessing = false; // 重置处理标志
				uni.$off('xwscan', this.BroadcastScanningToObtainData);

				if (type == '1') {
					// 重新扫描 - 直接设置，不使用异步操作
					this.tips = '请重新扫描条码';
					console.log('设置重新扫描提示');
				}
			},

			/**
			 * 扫一扫功能
			 */
			search() {

				if(!this.show) {
					this.show = true;
					this.openModal(); // 初始化弹窗数据和标题

					if(this.vuex_config.deviceType === this.vuex_config.deviceTypePDA){
						this.$nextTick(() => {
						this.isInputFocused = false; // 控制输入框聚焦
						// 短暂延迟后取消聚焦状态，避免后续问题
						setTimeout(() => {
								this.isInputFocused = true;
							}, 100);
						});
					} else {
						this.initBroadcastListener();
					}
				}

				// #ifdef APP-PLUS
				let _that = this;
				uni.scanCode({
					scanType: ["barCode", "qrCode"],
					// onlyFromCamera: true,
					success: function(res) {
						console.log("条码类型：" + res.scanType);
						console.log("条码内容：" + res.result);
						_that.barCode = res.result + '\n';
						_that.confirm()
						// 检查是否需要继续自动扫码
						if( _that.vuex_config.workScan ){
							let shouldContinueScanning = false;

							// repCheck 流程：允许一直回调 search 方法
							if (_that.queryData.funcType === _that.vuex_config.flowTypeEnum.repCheck) {
								shouldContinueScanning = true;
							}
							// beginWork 和 endWork 流程：检查当前条码数量，少于2个才继续扫码
							else if (_that.queryData.funcType === _that.vuex_config.flowTypeEnum.beginWork ||
									 _that.queryData.funcType === _that.vuex_config.flowTypeEnum.endWork) {
								let lines = _that.inputRef.split(/[(\r\n)\r\n]+/);
								let validLines = lines.filter(item => item.trim() !== '');
								shouldContinueScanning = validLines.length < 2;
								console.log('beginWork/endWork 当前条码数量:', validLines.length, '是否继续扫码:', shouldContinueScanning);
							}
							// 其他流程：enterCount < 2 时才回调
							else {
								shouldContinueScanning = _that.enterCount < 2;
							}

							if (shouldContinueScanning) {
								setTimeout(()=>{
									_that.search()
								},500)
							}
						}
					},
				});
				// #endif
				// #ifdef H5 || MP-WEIXIN
				this.tips = '请在APP中使用扫码功能';
				// #endif
			},
			
			/**
			 * 结束扫描（与 machine/index.vue 的 endScan 方法完全一致）
			 */
			endScan(type) {
				console.log('=== endScan 方法被调用 ===');
				console.log('传入的 type 参数:', type);
				console.log('当前 inputRef:', this.inputRef);
				console.log('当前 funcType:', this.queryData.funcType);

				if (this.inputRef == '') {
					console.log('inputRef 为空，退出 endScan');
					this.tips = '请扫描条码';
					return;
				}

				let that = this;
				let lines = this.inputRef.split(/[(\r\n)\r\n]+/);
				lines.forEach((item, index) => {
					// 删除空项
					if (item === '') {
						lines.splice(index, 1);
					}
				});
				this.enterCount++;

				// 当 funcType 为 repCheck 时，允许多条 99- 开头的条码用逗号隔开
				if (this.queryData.funcType == this.vuex_config.flowTypeEnum.repCheck) {
					let barPicItems = [];
					lines.forEach((item) => {
						if (item.startsWith('98-')) {
							this.queryData.barUser = item;
						} else if (item.startsWith('99-')) {
							barPicItems.push(item);
						}
					});
					// 多条 99- 开头的条码用逗号隔开
					if (barPicItems.length > 0) {
						this.queryData.barPic = barPicItems.join(',');
					}
				} else {
					// 其他情况保持原有逻辑不变
					lines.forEach((item) => {
						if (item.startsWith('98-')) {
							this.queryData.barUser = item;
						} else if (item.startsWith('99-')) {
							this.queryData.barPic = item;
						}
					});
				}
				// workScan 是否需要扫描员工工牌设置
				if(!this.vuex_config.workScan){
					if(this.enterCount == '1' || type == 'do'){
						if (!this.queryData.barPic) {
						    this.tips = '图纸条码不正确!';
						    // this.clearText();
							// this.enterCount = 0;
							this.sendMp3('sb');
							return false;
						}
						
						this.$u.api.m8.checkFlowBar({
							...this.queryData,
							barUser:'98-' + this.vuex_user.userCode
						}).then((res) => {
							if(res.result == 'true' && !res.data.errorMsg){
								this.sendMp3('cg');

								if(this.queryData.funcType == this.vuex_config.flowTypeEnum.beginWork){

									if(res.data.flowList && res.data.flowList.length == 1){

										uni.navigateTo({
											url: '/pages/m8/gongxu/beginForm',
											events: {
												Filter(data) {
												}
											},
											success: function(resq) {
												resq.eventChannel.emit('beginForm', res.data)
											}
										})
									} else {
										uni.navigateTo({
											url: '/pages/m8/gongxu/selectList',
											events: {
												Filter(data) {
												}
											},
											success: function(resq) {
												resq.eventChannel.emit('selectList', res.data || {})
											}
										})
									}
								}
								if(this.queryData.funcType == this.vuex_config.flowTypeEnum.endWork){
									uni.navigateTo({
										url: '/pages/m8/gongxu/endForm',
										events: {
											Filter(data) {
											}
										},
										success: function(resq) {
											resq.eventChannel.emit('endWork', res.data)
										}
									})
								}

								// 送检
								if(this.queryData.funcType == this.vuex_config.flowTypeEnum.repCheck){
									uni.navigateTo({
										// url: '/pages/m8/repCheck/list?params='+JSON.stringify(res.data.repCheck),
										url: '/pages/m8/gongxu/repCheckList',
										events: {
											Filter(data) {
											}
										},
										success: function(resq) {
											resq.eventChannel.emit('repCheck', res.data)
										}
									});
									// repCheck 流程允许一直扫描，只清除输入内容，不关闭弹窗
									// this.clearText('1');
									return; // 不关闭弹窗，允许继续扫描
								}

								this.show = false;
								
								
							}else{
								this.sendMp3('sb');
								this.tips = res.message || res.data.errorMsg
							}
						});
					}
				}else{
					if(this.enterCount == '2' || type == 'do'){
						if (!this.queryData.barPic) {
						    this.tips = '图纸条码不正确!';
						    // this.clearText();
							// this.enterCount = 0;
							this.sendMp3('sb');
							return false;
						}
						if (!this.queryData.barUser) {
						    this.tips = '用户条码不正确!';
						    // this.clearText();
							// this.enterCount = 0;
							this.sendMp3('sb');
							return false;
						}

						this.$u.api.m8.checkFlowBar(this.queryData).then((res) => {
							if(res.result == 'true' && !res.data.errorMsg){
								this.sendMp3('cg');
								console.log('checkFlowBar:', res);
								console.log('funcType',this.queryData.funcType,this.vuex_config.flowTypeEnum)
								if(this.queryData.funcType === this.vuex_config.flowTypeEnum.beginWork){


									if(res.data.flowList && res.data.flowList.length == 1){

										uni.navigateTo({
											url: '/pages/m8/gongxu/beginForm',
											events: {
												Filter(data) {
												}
											},
											success: function(resq) {
												resq.eventChannel.emit('beginForm', res.data)
											}
										})
									} else {
										uni.navigateTo({
											url: '/pages/m8/gongxu/selectList',
											events: {
												Filter(data) {
												}
											},
											success: function(resq) {
												resq.eventChannel.emit('selectList',  res?.data || {})
											}
										})
									}
								}
								if(this.queryData.funcType === this.vuex_config.flowTypeEnum.endWork){
									uni.navigateTo({
										url: '/pages/m8/gongxu/endForm',
										events: {
											Filter(data) {
											}
										},
										success: function(resq) {
											resq.eventChannel.emit('endWork', res.data)
										}
									})
								}

								if(this.queryData.funcType === this.vuex_config.flowTypeEnum.repCheck){
									uni.navigateTo({
										// url: '/pages/m8/repCheck/list?params='+JSON.stringify(res.data.repCheck),
										url: '/pages/m8/gongxu/repCheckList',
										events: {
											Filter(data) {
											}
										},
										success: function(resq) {
											resq.eventChannel.emit('repCheck', res.data)
										}
									});
									// repCheck 流程允许一直扫描，只清除输入内容，不关闭弹窗
									this.clearText('1');
									return; // 不关闭弹窗，允许继续扫描
								}

								this.show = false;
								
							}else{
								this.sendMp3('sb');
								this.tips = res.message || res.data.errorMsg
							}
						});
					}
				}
			},

			/**
			 * 广播扫描数据处理（与 machine/index.vue 的 BroadcastScanningToObtainData 方法一致）
			 */

			BroadcastScanningToObtainData(res) {
				console.log('广播扫描数据:', res);

				if (!res || !res.code) {
					return;
				}

				// 防抖处理：如果正在处理中，跳过
				if (this.isProcessing) {
					console.log('正在处理中，跳过重复调用');
					return;
				}

				//获取扫描到的条形码
				let barcode = res.code;

				//判断条形码长度是否大于3
				if (barcode.length > 3) {
					//去除换行符
					let newString = barcode.replace('\n;', '');
					let processedCode = newString + '\n';

					// 防重复处理：检查是否已经处理过相同的条码
					if (this.barCode === processedCode) {
						console.log('重复的条码，跳过处理:', processedCode);
						return;
					}

					// 设置处理标志
					this.isProcessing = true;

					this.barCode = processedCode;
					this.confirm();

					// 延迟重置处理标志
					setTimeout(() => {
						this.isProcessing = false;
					}, 1000);
				}
			},
			confirm() {
				console.log('=== confirm 方法开始 ===');
				console.log('当前 funcType:', this.queryData.funcType);
				console.log('beginWork 枚举值:', this.vuex_config.flowTypeEnum.beginWork);
				console.log('endWork 枚举值:', this.vuex_config.flowTypeEnum.endWork);
				console.log('当前 barCode:', this.barCode);
				console.log('当前 inputRef:', this.inputRef);

				// 始终添加条码到输入框，无论是第几次扫码
				if (this.barCode) {
					// 检查是否重复条码
					if (!this.inputRef.includes(this.barCode.trim())) {
						this.inputRef += this.barCode;
						console.log('条码已添加，更新后的 inputRef:', this.inputRef);
					} else {
						console.log('条码重复，但仍然显示在界面上');
						// 即使重复，也要确保界面显示最新的条码
						this.inputRef += this.barCode;
					}
				} else {
					console.log('条码为空，无法添加');
				}

				// 检查是否需要自动调用 endScan
				// beginWork 和 endWork 流程：扫码2次后自动调用 endScan
				if (this.queryData.funcType === this.vuex_config.flowTypeEnum.beginWork ||
					this.queryData.funcType === this.vuex_config.flowTypeEnum.endWork) {

					console.log('匹配到 beginWork 或 endWork 流程');

					// 计算当前已扫描的条码数量
					let lines = this.inputRef.split(/[(\r\n)\r\n]+/);
					let validLines = lines.filter(item => item.trim() !== '');

					console.log('分割后的 lines:', lines);
					console.log('过滤后的 validLines:', validLines);
					console.log('当前扫码数量:', validLines.length);

					// 扫码2次后自动调用 endScan，但不阻止后续扫码显示
					if (validLines.length === 2) {
						console.log('条码数量达到2个，自动调用 endScan');
						// 延迟调用，确保界面先更新
						setTimeout(() => {
							this.endScan('do');
						}, 100);
					} else {
						console.log('条码数量:', validLines.length, '不自动调用 endScan');
					}
				} else {
					console.log('不是 beginWork 或 endWork 流程，不自动调用 endScan');
				}
				// 其他流程（如 repCheck）需要手动调用 endScan

				// 清理条码数据
				setTimeout(() => {
					this.barCode = '';
				}, 500);

				console.log('=== confirm 方法结束 ===');
			},
		}
	};
</script>

<style lang="scss" scoped>
	// .modal-container {
	// 	background-color: #ffffff;
	// 	border-radius: 10rpx;
	// 	overflow: hidden;
	// }

	// // 与 machine/index.vue 保持一致的样式
	// .btn {
	// 	border-radius: 8rpx !important;
	// 	font-size: 28rpx !important;
	// 	padding: 20rpx 30rpx !important;
	// }
</style>
