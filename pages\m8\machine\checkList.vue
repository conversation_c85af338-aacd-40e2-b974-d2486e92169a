<template>
	<view>
		<js-error mode="bottom" ref="jsError"></js-error>
		<!-- <u-sticky class="u-sticky">
			<view class="padding-sm flex light " style="background-color: #eee;" @tap="show=true">
				<view style="width: 100%"><u-search placeholder="物料名称/代码/规格型号等" v-model="keyword" bg-color="#fff"
						:show-action="false" :disabled="true" @tap="show=true" searchIconSize="26"
						:inputStyle="inputStyle"></u-search>
				</view>
			</view>
		</u-sticky> -->
		<!-- <view class="search">
			<u-search v-model="query['oper.operName']" :show-action="false" @search="loadData" placeholder="请输入工序搜索" ></u-search>
		</view> -->
		<u-popup v-model="show" mode="right" length="90%">
			<!-- class="form"  -->
			<u-form class="form" style="padding: 0 10px;" :model="query" ref="uForm" label-position="left">
				
				<view style="" class="text-border">
					<view class="text text-lg text-green">基本信息</view>
				
					<u-form-item  label="物料代码:" prop="viewCode" label-width="230">
						<u-input placeholder="请输入" v-model="query['viewCode']" type="text" maxlength="200"></u-input>
					</u-form-item>
					<u-form-item  label="物料名称:" prop="invName" label-width="230">
						<u-input placeholder="请输入" v-model="query['invName']" type="text" maxlength="200"></u-input>
					</u-form-item>
					<u-form-item  label="规格型号:" prop="invStd" label-width="230">
						<u-input placeholder="请输入" v-model="query['invStd']" type="text" maxlength="200"></u-input>
					</u-form-item>
					<u-form-item  label="客户图号:" prop="f665" label-width="230">
						<u-input placeholder="请输入" v-model="query['f665']" type="text" maxlength="200"></u-input>
					</u-form-item>
				</view>
			</u-form>
			
			<view class="cu-bar"></view>
			<view class=" flex cu-bar tabbar bg-white foot cu-modal-footer">
				<button class="cu-btn  lines-red lg " @click="reset">重置</button>
				<button class="cu-btn  bg-confirm lg margin-left " @click="submit">查询</button>
			</view>
		</u-popup>
		
		<scroll-view scroll-y="true" @scrolltolower="loadMore" :refresher-enabled="true" :scrolltolower-enabled="true"
			:refresher-triggered="triggered" @refresherrefresh="refresherrefresh" @scrolltoupper="refresherrefresh"
			class="scroll-view-class" :style="{ height: computedScrollViewHeight }">
			<view class="scroll-content" ref="scrollContent" style="
          display: flex;
          flex-direction: column;
          justify-content: space-between;
		  width: 100%;
        " :style="{ height: computedScrollViewHeight  }">
			<u-radio-group v-model="value">
				
				<view v-for="(item,index) in list"   @click="addData(item,index)" class="cu-item shadow " style="position: relative;margin-bottom: 10px;width: 100%;" :key="item.id">
					<view class="cu-form-group"  style="display: flex;justify-content: space-between;">
						<view
							style=" display: inline-block;padding: 5px;background: #3E97B0;color: #fff;min-width: 40px;text-align: center;font-style: italic;font-weight: bold">
							{{ index + 1 }}
						</view>
						<!-- <view class="text-red text-bold"> {{ item.taskCode|| ""  }}</view> -->
						<view class="">
							<dictLabel style="margin-left: 10px;" :value="item.taskStatus" dict-type="m8_task_status">
							</dictLabel>
						</view>
					</view>
					<!-- <view class="cu-form-group">
						<view class="text-xl text-bold">{{ item.starttime || "" }} <text style="margin: 0 10px;">至</text>  {{ item.endtime || "" }}</view>
					  </view> -->
					<!-- <view class="cu-form-group">
						<view class="title">任务：</view>
						<view style="flex: 1;"> {{ item.taskCode|| ""  }}</view>
					</view> -->
					<view class="cu-form-group">
						<view class="title">工序：</view>
						<view style="flex: 1;">({{ item.sortNum || ''}}) {{ item.oper.operName || ""  }}</view>
					</view>
					
					<view class="cu-form-group text-red text-bold">
						<view class="title">数量：</view>
						<view style="flex: 1;"> <!-- {{ item.iqty || ""  }} -->
							<!-- <u-input /> -->
							<u-input v-model="item.iqty" type="number" placeholder="请输入" clearable />
						</view>
						<!-- <u-radio class="rights" :name="index"></u-radio> -->
					</view>
					<view class="cu-form-group">
						<view class="title">操作员：</view>
						<view style="flex: 1;"> {{ item.machine  || ""  }}</view>
					</view>
					<view class="cu-form-group">
						<view class="title">完工时间：</view>
						<view style="flex: 1;"> {{ item.machine  || ""  }}</view>
					</view>
					
				</view>
				</u-radio-group>

				<view v-if="list.length" class="loadmore" style="justify-self: flex-end">
					<u-loadmore :status="loadStatus"></u-loadmore>
				</view>
				<u-empty v-if="!list.length" style="height: 80vh;"></u-empty>
			</view>
		</scroll-view>

		<view>
			<movable-area class="movable-area">
				<movable-view class="movable-view" :x="x" :y="y" direction="all">
					<u-button size="mini" @click="toAdd" type="primary"
						style="width: 90px; height: 70px; color: #fff; font-size: 20px">确认
					</u-button>
				</movable-view>
			</movable-area>
		</view>
	</view>
</template>
<script>
	import {
		hasPermission
	} from '@/common/fire.js'
	import dictLabel from "@/components/dictLabel.vue";
	export default {
		components: {
			dictLabel,
		},
		data() {
			return {
				x: 650, //x坐标
				y: 650, //y坐标
				value: null,
				radioValue:{},
				model:{},
				companySelectList:[],
				show: false,
				smshow: false,
				focus: true,
				barCode: "",
				remarks: '',
				czData: {},
				show2: false,
				type: 'text',
				startTime: false, //控制日期显示
				endTime: false, //控制日期显示
				show: false,
				inputStyle: {
					fontSize: '34rpx'
				},
				filterIcon: "/static/image/filter.png",
				keyword: "",
				background: {
					backgroundColor: '#3E97B0',
				},
				list: [],
				itemContent: {},
				query: {
					pageNo: 1,
					pageSize: 5,
					// companyCode:this.vuex_company.companyCode || '',
					// 'company.companyName':this.vuex_company.companyName || '',
					// company:{
					// 	companyName:this.vuex_company.companyName || '',
					// }
					// orderBy: "a.create_date desc",
				},
				loadStatus: "loadmore",
				triggered: false,
				// flag: hasPermission('app:proj:weekly:pmWeekly:edit'),
				flag: false,
				scrollViewHeight: 0,
				headerHeight: 0,
				projStatus: this.$store.state.auth.projStatus,
			};
		},
		onShow() {
			let that = this
			that.query.pageNo = 1;
			that.loadData();
			// this.query.companyCode = this.vuex_company.companyCode || '';
			// this.query['company.companyName ']= this.vuex_company.companyName || '';
			// this.query.pageNo = 1;
			// this.loadData();
			
			// this.$u.api.ktnw.companyTreeData().then(res => {
			// 	this.companySelectList = res;
			// });
		},
		onLoad(e) {
			
			let that = this
			const eventChannel = this.getOpenerEventChannel();
			eventChannel.on('shangJi', function(data) {
				that.model = data
				that.query['orderPic.id'] = data.orderPic.id;
				// that.query['orderPic.picno'] = data.orderPic.picno || ''
				// that.query['orderPic.picname'] = data.orderPic.picname || ''
				that.query.pageNo = 1;
				that.loadData();
				that.$forceUpdate()
			})
			
			// this.query['orderPic.picno'] = e.picno || ''
			// this.query['orderPic.picname'] = e.picname || ''
			// this.query.pageNo = 1;
			// this.loadData();
		},
		mounted() {
			// this.$refs.xmInfo.$on('child-mounted-done', () => {
			//   this.calculateScrollViewHeight();
			// });
			this.calculateScrollViewHeight();
		},
		computed: {
			computedScrollViewHeight() {
				return this.scrollViewHeight === 'auto' ? 'auto' : `${this.scrollViewHeight}px`;
			},
		},
		methods: {
			addData(item,index) {
				this.value = index
				this.radioValue = item
			},
			toAdd() {
				console.log(this.radioValue.taskCode,'this.radioValue.taskCode======');
				if (this.radioValue.taskCode != undefined && this.radioValue.taskCode) {
					this.xzgx(this.radioValue)
				} else {
					this.$u.toast("请选择至少一条数据！");
					return;
				}
			},
			xzgx(item){
				let that = this
				if(item.taskStatus == '3'){
					uni.showModal({
						title: '提示',
						content: '当前任务已完成,是否继续加工？',
						confirmColor: '#F54E40',
						success: (res) => {
							if (res.confirm) {
								
								let data = {
									...that.model,
									oper:item.oper,
									task:item
								}
								
								uni.navigateTo({
									url: '/pages/m8/shangJi/form',
									success: function(resq) {
										resq.eventChannel.emit('shangJi2', data)
									}
								})
								// uni.$emit('xzgx',item);
								// uni.navigateBack({
								// 	delta: 1,
								// });
								return false;
							}
						}	
					})
				}else if(item.taskStatus == '2'){
					uni.showModal({
						title: '提示',
						content: '当前任务正在其它机床加工,是否继续？',
						confirmColor: '#F54E40',
						success: (res) => {
							if (res.confirm) {
								let data = {
									...that.model,
									oper:item.oper,
									task:item
								}
								
								uni.navigateTo({
									url: '/pages/m8/shangJi/form',
									success: function(resq) {
										resq.eventChannel.emit('shangJi2', data)
									}
								})
								// uni.$emit('xzgx',item);
								// uni.navigateBack({
								// 	delta: 1,
								// });
								return false;
							}
						}
					})
				}else if(item.taskStatus == '9'){
					uni.showModal({
						title: '提示',
						content: '当前任务已暂停,是否继续加工？',
						confirmColor: '#F54E40',
						success: (res) => {
							if (res.confirm) {
								let data = {
									...that.model,
									oper:item.oper,
									task:item
								}
								
								uni.navigateTo({
									url: '/pages/m8/shangJi/form',
									success: function(resq) {
										resq.eventChannel.emit('shangJi2', data)
									}
								})
								// uni.$emit('xzgx',item);
								// uni.navigateBack({
								// 	delta: 1,
								// });
								return false;
							}
						}	
					})
				}else{
					let data = {
						...that.model,
						oper:item.oper,
						task:item
					}
					
					uni.navigateTo({
						url: '/pages/m8/shangJi/form',
						success: function(resq) {
							resq.eventChannel.emit('shangJi2', data)
						}
					})	
				}
				
			},
			toForm(item){
				const that = this
				uni.navigateTo({
					url: '/pages/xy/asnH/form?djno=' + item.djno,
				})
			},
			submit() {
				setTimeout(() => {
					this.query.pageNo = 1;
					this.loadData();
					this.show = false
				}, 100);
			},
			
			// this.$forceUpdate();
			reset() {
				this.list = [];
				this.query = {
					pageNo: 1,
					pageSize: 20,
				};
				this.loadData();
				this.show = false
			},
			
			startConfirm(e) {
				this.query.arrDate_gte = e.year + '-' + e.month + '-' + e.day;
			},
			endConfirm(e) {
				this.query.arrDate_lte = e.year + '-' + e.month + '-' + e.day;
			},
			customBack() {
				// 跳转到工作台页面
				// uni.navigateTo({
				//   url: "/pages/zfgs/index/index/index?item=" + JSON.stringify(this.itemContent),
				// });
				uni.navigateBack({
					delta: 1,
				});
			},
			async calculateScrollViewHeight() {
				try {
					// 等待头部高度计算完成
					// await new Promise((resolve) => {
					//   this.$nextTick(() => {
					//     this.headerHeight = this.$refs.xmInfo.$refs['u-sticky'].height + this.$refs.xmInfo.$refs['u-sticky'].h5NavHeight + this.$refs.navbar.navbarHeight;
					//     resolve();
					//   });
					// });
					this.headerHeight = 0
					// 计算tabBar高度
					if (this.flag) {
						this.tabBarHeight = 60; // 假设tabbar高度为50px，实际应根据实际情况获取
					} else {
						this.tabBarHeight = 0;
					}
					// 获取屏幕高度
					const systemInfo = uni.getSystemInfoSync();
					const screenHeight = systemInfo.windowHeight;
					// 计算scroll-view高度
					this.scrollViewHeight = screenHeight - (this.headerHeight + this.tabBarHeight);
				} catch (error) {
					console.error('Error while calculating ScrollView height:', error);
				}
			},

			loadMore() {
				this.loadStatus = "loading";
				setTimeout(() => {
					this.query.pageNo += 1;
					this.loadData("add");
				}, 100);
			},
			loadData(type) {
				// 循环10次重复赋值  needShListData
				this.$u.api.m8.taskLastNodeData(this.query).then((res) => {
					if (res.list.length >= 0) {
						// this.xmList = res.list
						if (res.list.length < 20 || res.list.length == 0) {
							this.loadStatus = "nomore";
						}
						var data = res.list;
						if (type == "add") {
							for (var i = 0; i < data.length; i++) {
								this.list.push(data[i]);
							}
						} else {
							this.list = data;
						}
					}
				});
			},
			async refresherrefresh() {
				this.triggered = true;
				this.query.pageNo = 1;
				await this.loadData();
				const scrollTop = 0
				uni.pageScrollTo({
					scrollTop,
					duration: 0,
				});
				setTimeout(() => {
					this.triggered = false;
				}, 500);
			},
		},
		// beforeDestroy() {
		// 	// 清理事件监听器
		// 	try {
		// 		this.$refs.xmInfo.$off('child-mounted-done');
		// 	} catch (e) {
		// 		console.log('Error while unbinding event:', e);
		// 	}
		// }
	};
</script>
<style lang="scss" scoped>
	$all_width: 96rpx;
	$all_height: 96rpx;
	.movable-area {
		z-index: 999;
		height: 97vh;
		width: 650rpx;
		// max-width: 1152rpx;
		// 固定在右边
		top: -20rpx;
		position: fixed;
		right: $all_width;
		// 固定在右下角
		pointer-events: none; //此处要加，鼠标事件可以渗透
	
		.movable-view {
			width: $all_width;
			height: $all_height;
			pointer-events: auto; //恢复鼠标事件
	
			image {
				width: $all_width;
				height: $all_height;
			}
		}
	}
	/deep/.u-radio__icon-wrap[data-v-da6758f0] {
		width: 30px !important;
		height:30px !important;
	}
	
	.text-border{
		border:2px solid  rgb(170, 170, 170); 
		border-radius: 5px;
		position: relative;
		padding-top: 20rpx;
		margin-top: 40rpx;
		padding-left: 20rpx;
		.text{
			position: absolute;
			background-color: #fff;
			padding: 10rpx 10rpx;
			top: -25rpx;
			right: 30rpx;
		}
	}
	.custom-style{
		// background-color: rgba(25, 190, 107, 0.7);
		
	}	
	.text-xxl{
		font-size:60rpx;
	}
	.cu-bar {
		min-height: 60px;
	}
	
	.cu-btn {
		font-size: 16px;
		height: 40px;
	}
	
	.foot {
		background: #fff;
	}
	
	.cu-item {
		flex-wrap: wrap;
		padding: 8px 15px !important;
	}
	
	.title {
		width: 200rpx;
	}
	
	.cu-modal-slot {
		margin: 42rpx 32rpx 20rpx 32rpx;
		height: 100vh;
		position: relative;
	}
	
	.cu-modal-header {
		font-size: 38rpx;
		color: #3d3d3d;
		line-height: 40rpx;
		text-align: left;
		font-weight: 500;
	}
	
	.button {
		font-size: 32rpx;
		color: #666666;
		line-height: 40rpx;
		padding: 12rpx 40rpx;
		margin-bottom: 20rpx;
		background: #e2e2e2;
		border-radius: 180rpx;
	}

	.button:hover {
		background: #3e97b0;
		color: #ffffff;
	}

	.cu-modal-search-title {
		margin: 32rpx 0rpx 20rpx 0rpx;
		text-align: left;
		font-size: 32rpx;
		color: #999999;
		line-height: 40rpx;
	}

	.cu-dialog {
		background: #ffffff;
	}

	.cu-modal-footer {
		padding: 32rpx 32rpx;
		width: 100%;

		.cu-btn {
			width: 50%;
		}
	}

	.bg-confirm {
		background: #3e97b0;
		color: #ffffff;
	}
</style>