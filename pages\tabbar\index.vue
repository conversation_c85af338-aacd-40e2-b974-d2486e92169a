<template>
	<view class="tabbar-container">
		<xw-scan></xw-scan>
		<!-- 页面内容区域  -->
		<view class="page-content" >
			<!-- 使用 v-show 方式显示页面组件，避免动态组件的问题 -->
			<machine-list ref="machineList" v-show="currentPageComponent === 'MachineList'" />
			<workshop-list v-show="currentPageComponent === 'WorkshopList'" />
			<warehouse-list v-show="currentPageComponent === 'WarehouseList'" />
			<process-list v-show="currentPageComponent === 'ProcessList'" />
			<user-center v-show="currentPageComponent === 'UserCenter'" />

			<!-- 默认页面（当没有匹配的组件时） -->
			<view v-show="!currentPageComponent || !['MachineList', 'WorkshopList', 'WarehouseList', 'ProcessList', 'UserCenter'].includes(currentPageComponent)" class="default-page">
				<view class="default-content">
					<text class="default-text">页面加载中...</text>
					<text class="debug-text">组件: {{ currentPageComponent }}</text>
					<text class="debug-text">索引: {{ currentIndex }}</text>
				</view>
			</view>
		</view>

		<!-- 动态 uView TabBar -->
		<dynamic-u-tab-bar
			@change="onTabBarChange"
			@update:currentIndex="onCurrentIndexUpdate"
			:activeColor="activeColor"
			:inactiveColor="inactiveColor"
			:bgColor="bgColor"
			:iconSize="iconSize"
			:fontSize="fontSize"
			:height="tabBarHeight"
			:currentIndex="currentIndex"
			ref="dynamicTabBar"
		/>
	</view>
</template>

<script>
	import { mapState } from 'vuex';
	import DynamicUTabBar from '@/components/DynamicUTabBar/DynamicUTabBar.vue';
	import menuMatcher from '@/utils/menuMatcher.js';
	import checkVersion from "@/pages/lq-upgrade/checkVersion.js";

	// 导入各个页面组件
	import MachineList from './components/MachineList.vue';
	import WorkshopList from './components/WorkshopList.vue';
	import WarehouseList from './components/WarehouseList.vue';
	import ProcessList from './components/ProcessList.vue';
	import UserCenter from './components/UserCenter.vue';

	export default {
		name: 'TabBarIndex',
		components: {
			DynamicUTabBar,
			MachineList,
			WorkshopList,
			WarehouseList,
			ProcessList,
			UserCenter
		},
		data() {
			return {
				currentIndex: 0,
				currentTabBarItem: null, // 保存当前的 TabBar 项目
				activeColor: '#007aff',   // 更明显的蓝色
				inactiveColor: '#c0c4cc', // 更浅的灰色，增强对比
				bgColor: '#ffffff',
				iconSize: '30px',        // 图标大小
				fontSize: '18px',        // 字体大小（您已调整）
				tabBarHeight: '60px',    // TabBar 高度
				systemInfo: null,        // 系统信息

				// 页面组件映射配置
				pageComponentMap: {
					'pages/m8/machine/list': 'MachineList',
					'pages/sys/jcList': 'WorkshopList',
					'pages/sys/list': 'WarehouseList',
					'pages/sys/gxList': 'ProcessList',
					'pages/sys/index/user/index': 'UserCenter',
					'pages/sys/userCenter': 'UserCenter'
				}
			};
		},
		computed: {
			...mapState(['vuex_tabBarList']),

			/**
			 * 计算页面内容区域的高度
			 */
			pageContentHeight() {
				// 将 tabBarHeight 的 px 值转换为数字
				const tabBarHeightValue = parseInt(this.tabBarHeight) || 60;

				// 使用更精确的方法计算导航栏高度
				let navigationBarHeight = 0;
				if (this.systemInfo) {
					// screenHeight - windowHeight = 状态栏 + 导航栏的总高度
					const topBarHeight = this.systemInfo.screenHeight - this.systemInfo.windowHeight;
					navigationBarHeight = topBarHeight;

					console.log('导航栏高度计算:', {
						screenHeight: this.systemInfo.screenHeight,
						windowHeight: this.systemInfo.windowHeight,
						topBarHeight: topBarHeight,
						statusBarHeight: this.systemInfo.statusBarHeight,
						platform: this.systemInfo.platform
					});
				} else {
					// 备用方案
					navigationBarHeight = 88; // 状态栏 + 导航栏的大概高度
				}

				// 考虑安全区域（主要是 iPhone X 系列的底部安全区域）
				let safeAreaBottom = 0;
				if (this.systemInfo && this.systemInfo.safeAreaInsets) {
					safeAreaBottom = this.systemInfo.safeAreaInsets.bottom || 0;
				}

				// 总的需要减去的高度 = 导航栏高度 + TabBar高度 + 底部安全区域
				const totalReduceHeight = navigationBarHeight + tabBarHeightValue + safeAreaBottom;

				console.log('高度计算:', {
					navigationBarHeight: navigationBarHeight,
					tabBarHeight: tabBarHeightValue,
					safeAreaBottom: safeAreaBottom,
					totalReduceHeight: totalReduceHeight,
					systemInfo: this.systemInfo
				});

				return `calc(100vh - ${totalReduceHeight}px)`;
			},

			/**
			 * 当前页面组件名称
			 */
			currentPageComponent() {
				// 优先使用从 DynamicUTabBar 传递过来的当前项目
				let currentItem = this.currentTabBarItem;

				// 如果没有当前项目，则从 vuex_tabBarList 中获取
				if (!currentItem && this.vuex_tabBarList && this.vuex_tabBarList.length > 0) {
					currentItem = this.vuex_tabBarList[this.currentIndex];
				}

				if (!currentItem) {
					// 如果没有菜单项，检查是否是空菜单的情况（只有"我的"菜单会通过DynamicUTabBar处理）
					if (this.vuex_tabBarList && this.vuex_tabBarList.length === 0) {
						console.log('没有菜单数据，默认显示用户中心（只有"我的"菜单的情况）');
						return 'UserCenter';
					}
					console.log('没有当前菜单项，默认显示机床页面');
					return 'MachineList'; // 默认显示机床页面
				}

				// 获取页面路径，优先使用 originalPagePath
				const pagePath = this.normalizePagePath(
					currentItem.originalPagePath ||
					currentItem.menuUrl ||
					currentItem.pagePath ||
					''
				);

				// 从映射表中查找对应的组件名称
				const componentName = this.pageComponentMap[pagePath];

				// 如果找不到对应组件，根据菜单情况决定默认组件
				let finalComponent;
				if (componentName) {
					finalComponent = componentName;
				} else {
					// 检查是否是"我的"菜单相关
					const isUserMenu = pagePath.includes('user') ||
									  currentItem.extend?.extendS2 === 'menu:user:my' ||
									  currentItem.text === '我的' ||
									  currentItem.menuName === '我的';

					if (isUserMenu) {
						finalComponent = 'UserCenter';
						console.log('检测到用户菜单，显示用户中心');
					} else {
						finalComponent = 'MachineList';
					}
				}

				console.log(`TabBar切换: 索引${this.currentIndex} -> 组件${finalComponent} (路径: ${pagePath})`);
				console.log('当前菜单项:', currentItem);
				console.log('菜单总数:', this.vuex_tabBarList?.length || 0);

				return finalComponent;
			},

			/**
			 * 当前页面组件的唯一键，用于强制重新渲染
			 */
			currentPageKey() {
				// 使用索引和 TabBar 数据长度作为键，避免循环依赖
				const listLength = this.vuex_tabBarList ? this.vuex_tabBarList.length : 0;
				return `page-${this.currentIndex}-${listLength}`;
			}
		},
		watch: {
			// 监听 TabBar 数据变化
			vuex_tabBarList: {
				handler(newList) {
					if (newList && newList.length > 0) {
						console.log('TabBar数据更新，数量:', newList.length);
						// 确保当前索引不超出范围
						if (this.currentIndex >= newList.length) {
							console.log(`索引超出范围，重置: ${this.currentIndex} -> 0`);
							this.currentIndex = 0;
						}
						// 重置当前项目，让组件重新计算
						this.currentTabBarItem = null;
					}
				},
				immediate: true
			},

			// 监听当前页面组件变化，自动更新标题
			currentPageComponent: {
				handler(newComponent, oldComponent) {
					
					if (newComponent && newComponent !== oldComponent) {
						
						console.log(`currentPageComponent页面组件: ${oldComponent} -> ${newComponent}`);
						// 控制 MachineList 组件的广播监听事件
						this.handleMachineListBroadcast(newComponent, oldComponent);
						// 延迟更新标题，确保组件已经渲染
						this.$nextTick(() => {
							this.updateNavigationTitle();
						});
					}
				},
				immediate: true
			}
		},
		onLoad() {
			console.log('TabBar 容器页面加载');
			this.initPage();
			this.upgrade();
			this.getSystemInfo();
		},

		mounted() {
			this.getSystemInfo();
		},
		onShow() {
			console.log('TabBar 容器页面显示',this.currentPageComponent);
			// 触发页面显示事件
			uni.$emit('pageShow');
			// 更新导航栏标题
			this.updateNavigationTitle();
			// 监听颜色更新事件
			this.listenColorUpdate();
			
			if(this.currentPageComponent === 'MachineList'){
				this.handleMachineListBroadcast(this.currentPageComponent);
			}
			
		},
		onHide() {
			console.log('TabBar 容器页面隐藏');
			uni.$off('xwscan', this.broadcastScanningHandler);
		},
		onUnload() {
			console.log('TabBar 容器页面销毁');
			// 清理广播监听事件
			uni.$off('xwscan', this.broadcastScanningHandler);
		},
		methods: {
			//检查版本更新
			upgrade() {
			  // #ifdef APP-PLUS
			  this.$u.api.upgradeCheck().then((res) => {
			    let url = res.data.apkUrl
			      ? res.data.apkUrl
			      : this.vuex_config.xtUrl + res.data.xtUrl;
			    if (res.result == "true") {
			      checkVersion({
			        name: res.data.upTitle, //最新版本名称
			        code: res.data.upVersion, //最新版本号
			        content: `${res.data.upContent}`, //更新内容
			        url, //下载链接
			        // forceUpdate: true, //是否强制升级
			        forceUpdate: res.data.upType == '3'?true:false, //是否强制升级
			      });
			    }
			  });
			  // #endif
			},

			/**
			 * 获取系统信息
			 */
			getSystemInfo() {
				try {
					this.systemInfo = uni.getSystemInfoSync();
					console.log('系统信息:', this.systemInfo);
				} catch (e) {
					console.error('获取系统信息失败:', e);
				}
			},

			/**
			 * 初始化页面
			 */
			async initPage() {
				console.log('=== TabBar 容器页面初始化 ===');

				// 隐藏原生 TabBar（如果存在）
				try {
					uni.hideTabBar({
						animation: false,
						success: () => {
							console.log('隐藏原生 TabBar 成功');
						},
						fail: (error) => {
							console.log('隐藏原生 TabBar 失败（正常）:', error.errMsg);
						}
					});
				} catch (error) {
					console.log('隐藏原生 TabBar 异常（正常）:', error);
				}

				// 从本地存储加载菜单配置
				await this.loadTabBarFromLocalStorage();

				// 初始化当前索引
				this.updateCurrentIndex();

				console.log('=== TabBar 容器页面初始化完成 ===');
			},

			/**
			 * 从本地存储加载 TabBar 配置
			 */
			async loadTabBarFromLocalStorage() {
				console.log('尝试从本地存储加载 TabBar 配置...');

				try {
					// 获取本地存储的菜单数据
					const localData = menuMatcher.loadFromLocalStorage();

					// 获取原始 menu.json 中的菜单配置
					const originalMenus = menuMatcher.localMenuList || [];

					// 合并菜单配置：优先使用本地存储的数据，但保留原始 isShow=true 的项
					let finalMenus = [];

					if (localData.tabBarMenus && localData.tabBarMenus.length > 0) {
						// 使用本地存储的菜单作为基础
						finalMenus = [...localData.tabBarMenus];

						// 检查原始菜单中是否有 isShow=true 但不在本地存储中的项
						originalMenus.forEach(origItem => {
							if (origItem.isShow === true) {
								// 检查此项是否已在最终菜单中
								const exists = finalMenus.some(item =>
									item.extend?.extendS2 === origItem.extend?.extendS2
								);

								// 如果不存在，添加到最终菜单
								if (!exists) {
									finalMenus.push(origItem);
									console.log(`添加原始菜单项: ${origItem.text}`);
								}
							}
						});
					} else {
						// 如果本地存储中没有数据，使用原始菜单中 isShow=true 的项
						finalMenus = originalMenus.filter(item => item.isShow === true);
						console.log('使用原始菜单中的可见项:', finalMenus.length);
					}

					// 过滤出最终的可见菜单
					const visibleMenus = finalMenus.filter(item => item.isShow);

					if (visibleMenus.length > 0) {
						// 检查 Vuex 中是否已有数据（包括空数组）
						// 如果 Vuex 中的数据是 undefined 或 null，才使用本地配置
						// 如果是空数组，说明登录页面已经明确设置了空菜单，应该尊重这个设置
						if (this.vuex_tabBarList === undefined || this.vuex_tabBarList === null) {
							this.$store.commit('modifyTabBarList', visibleMenus);
							console.log('✓ Vuex 中无 TabBar 数据，加载本地配置成功:', visibleMenus.length, '项');
							console.log('可见菜单:', visibleMenus.map(item => item.text).join(', '));
						} else {
							console.log('✓ Vuex 中已有 TabBar 数据（包括空数组），不覆盖，当前数量:', this.vuex_tabBarList.length);
						}
						return true;
					}

					console.log('无可见菜单');
					return false;

				} catch (error) {
					console.error('从本地存储加载 TabBar 配置失败:', error);
					return false;
				}
			},
			
			/**
			 * TabBar 切换事件
			 */
			onTabBarChange(index, item) {
				console.log('=== TabBar 容器页面切换事件 ===');
				console.log(`切换: ${this.currentIndex} -> ${index}`);
				console.log('项目:', item);

				// 更新当前索引和项目
				this.currentIndex = index;
				this.currentTabBarItem = item;

				// 更新导航栏标题
				this.updateNavigationTitle(item.text);

				// 触发自定义事件
				this.$emit('tabChange', index, item);

				// 页面切换处理
				this.onPageSwitch(index, item);

				console.log('=== TabBar 容器页面切换事件结束 ===');
			},

			/**
			 * 当前索引更新事件
			 */
			onCurrentIndexUpdate(index) {
				console.log('TabBar 容器页面收到索引更新事件:', index);
				this.currentIndex = index;
			},
			
			/**
			 * 页面切换处理
			 */
			onPageSwitch(index, item) {
				console.log(`切换到页面: ${item.text} (索引: ${index})`);
				
				// 可以在这里添加页面切换时的特殊处理
				// 例如：数据刷新、状态重置等
				
				// 触发对应页面的激活事件
				this.$nextTick(() => {
					uni.$emit('pageActivated', {
						index: index,
						item: item,
						pagePath: item.pagePath
					});
				});
			},
			
			/**
			 * 更新当前索引
			 */
			updateCurrentIndex() {
				try {
					// 根据 Vuex 中的 TabBar 数据匹配索引
					if (this.vuex_tabBarList && this.vuex_tabBarList.length > 0) {
						// 默认显示第一个页面
						this.currentIndex = 0;
						console.log('设置默认索引:', this.currentIndex);
					}
				} catch (error) {
					console.error('更新当前索引失败:', error);
				}
			},
			
			/**
			 * 手动切换到指定索引
			 */
			switchToIndex(index) {
				if (index >= 0 && index < (this.vuex_tabBarList?.length || 5)) {
					this.currentIndex = index;
					console.log('手动切换到索引:', index);
				}
			},
			


			/**
			 * 更新导航栏标题
			 */
			updateNavigationTitle(title) {
				try {
					// 如果没有传入标题，根据当前显示的组件智能获取标题
					if (!title) {
						// 优先从 vuex_tabBarList 获取
						if (this.vuex_tabBarList && this.vuex_tabBarList.length > 0) {
							const currentItem = this.vuex_tabBarList[this.currentIndex];
							title = currentItem?.text || currentItem?.menuName;
						}

						// 如果 vuex_tabBarList 为空，根据当前显示的组件设置标题
						if (!title) {
							const componentTitleMap = {
								'MachineList': '机床',
								'WorkshopList': '车间',
								'WarehouseList': '仓库',
								'ProcessList': '工序执行',
								'UserCenter': '我的'
							};

							const currentComponent = this.currentPageComponent;
							title = componentTitleMap[currentComponent];

							console.log(`根据当前组件 ${currentComponent} 设置标题: ${title}`);
						}

						// 如果还是没有标题，使用默认标题
						if (!title) {
							const defaultTitles = ['机床', '车间', '仓库', '工序执行', '我的'];
							title = defaultTitles[this.currentIndex] || '主页';
						}
					}

					console.log('更新导航栏标题:', title);

					// 更新导航栏标题
					uni.setNavigationBarTitle({
						title: title,
						success: () => {
							console.log('✓ 导航栏标题更新成功:', title);
						},
						fail: (error) => {
							console.warn('✗ 导航栏标题更新失败:', error);
						}
					});
				} catch (error) {
					console.error('更新导航栏标题异常:', error);
				}
			},

			/**
			 * 监听颜色更新事件
			 */
			listenColorUpdate() {
				uni.$on('updateTabBarColors', (colors) => {
					console.log('收到 TabBar 颜色更新事件:', colors);
					if (colors.activeColor) {
						this.activeColor = colors.activeColor;
					}
					if (colors.inactiveColor) {
						this.inactiveColor = colors.inactiveColor;
					}
					if (colors.bgColor) {
						this.bgColor = colors.bgColor;
					}
					console.log('TabBar 颜色已更新:', {
						activeColor: this.activeColor,
						inactiveColor: this.inactiveColor,
						bgColor: this.bgColor
					});
				});
			},

			/**
			 * 控制 MachineList 组件的广播监听事件
			 */
			handleMachineListBroadcast(newComponent, oldComponent) {
				console.log(`控制广播监听事件: ${oldComponent} -> ${newComponent}`);

				// 当切换到 MachineList 组件时，手动开启广播监听
				if (newComponent === 'MachineList') {
					console.log('手动开启 MachineList 广播监听事件：xwscan');
					// 确保先移除之前的监听，避免重复监听
					uni.$off('xwscan', this.broadcastScanningHandler);

					// 使用 $nextTick 确保组件已经渲染完成后再开启监听
					this.$nextTick(() => {
						// 开启广播监听事件
						uni.$on('xwscan', this.broadcastScanningHandler);
						console.log('✓ MachineList 广播监听已开启');
					});
				}
				// 当从 MachineList 切换到其他组件时，关闭广播监听
				else if (oldComponent === 'MachineList' && newComponent !== 'MachineList') {
					console.log('手动关闭 MachineList 广播监听事件：xwscan');
					// 关闭广播监听事件
					uni.$off('xwscan', this.broadcastScanningHandler);
					console.log('✓ MachineList 广播监听已关闭');
				}
			},

			/**
			 * 广播扫描处理函数
			 */
			broadcastScanningHandler(res) {
				console.log('TabBar 容器接收到广播扫描事件:', res);
				console.log('当前页面组件:', this.currentPageComponent);

				// 只有当前显示的是 MachineList 组件时才处理
				if (this.currentPageComponent === 'MachineList') {
					// 使用 $nextTick 确保组件已经渲染完成
					this.$nextTick(() => {
						// 通过 ref 调用 MachineList 组件的处理方法
						const machineListRef = this.$refs.machineList;
						console.log('MachineList ref:', machineListRef);

						if (machineListRef) {
							console.log('MachineList 方法列表:', Object.getOwnPropertyNames(machineListRef));
							if (typeof machineListRef.BroadcastScanningToObtainData === 'function') {
								console.log('调用 MachineList.BroadcastScanningToObtainData');
								machineListRef.BroadcastScanningToObtainData(res);
							} else {
								console.warn('BroadcastScanningToObtainData 方法不存在');
								console.warn('可用方法:', Object.getOwnPropertyNames(machineListRef.__proto__));
							}
						} else {
							console.warn('MachineList 组件 ref 未找到');
							console.warn('所有 refs:', this.$refs);
						}
					});
				} else {
					console.log('当前不是 MachineList 组件，忽略广播事件');
				}
			},

			/**
			 * 标准化页面路径
			 */
			normalizePagePath(pagePath) {
				if (!pagePath) return '';

				// 移除开头的斜杠
				let normalized = pagePath.startsWith('/') ? pagePath.substring(1) : pagePath;

				// 确保路径格式正确
				if (!normalized.startsWith('pages/')) {
					normalized = `pages/${normalized}`;
				}

				return normalized;
			}
		}
	};
</script>

<style scoped lang="scss">
	.tabbar-container {
		width: 100%;
		// height: 100vh;
		display: flex;
		flex-direction: column;
		background-color: #f8f8f8;
		overflow: hidden; /* 防止整体容器出现滚动条 */
	}

	.page-content {
		/* 高度通过内联样式动态设置 */
		overflow-y: auto;
		overflow-x: hidden;
		position: relative;
		/* 隐藏滚动条但保持滚动功能 */
		scrollbar-width: none; /* Firefox */
		-ms-overflow-style: none; /* IE 和 Edge */

		/* 当内容不足时不显示滚动条 */
		&::-webkit-scrollbar {
			display: none; /* Chrome, Safari, Opera */
		}
	}

	.default-page {
		width: 100%;
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #f8f8f8;
	}

	.default-content {
		text-align: center;
		padding: 40rpx;
	}

	.default-text {
		font-size: 32rpx;
		color: #999999;
	}

	.debug-text {
		font-size: 24rpx;
		color: #666666;
		display: block;
		margin-top: 10rpx;
	}

	.debug-info {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 9999;
		background: rgba(0, 0, 0, 0.8);
		color: white;
		padding: 10rpx;
		font-size: 24rpx;
		text-align: center;
	}
</style>
