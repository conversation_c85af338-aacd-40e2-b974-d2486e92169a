<template>
	<view class="wrap ">
		<!-- <js-lang title="home.title" :showBtn="true"></js-lang> -->
		<!-- <u-swiper :height="300" :list="imgList" :title="false" @click="imgListClick"></u-swiper> -->

		<view v-if="loading" class="loading-container">
			<u-loading mode="circle" size="60"></u-loading>
			<text class="loading-text">加载工序执行菜单中...</text>
		</view>

		<view v-else-if="processMenuList.length > 0">
			<view v-for="res in processMenuList" :key="res.menuCode">
				<view class="cu-bar bg-white solid-bottom">
					<view class="action ">
						<u-icon name="/static/image/zfgs/index/icon@2x(6).png" size="80"></u-icon>
						<text class="text-lg  text-bold " style="font-size: 42rpx;">{{res.menuName}}</text>
					</view>
				</view>
				<view class="flex margin-sm flex-wrap justify-between u-skeleton">
					<view class="flex bg-white padding radius " v-for="item in res.childList"
						:key="item.menuName" @click="navigateToPage(item)" style="margin-bottom: 15rpx;">
						<view class="xm-item u-skeleton-rect">
							<view class="xm-item-1">{{item.menuName}}</view>
							<view class="xm-item-2">{{item.menuTitle}}</view>
						</view>
						<image style="width: 86rpx; height: 86rpx;" :src="item.menuIcon" mode="aspectFit"
							class="u-skeleton-circle" />
					</view>
				</view>
			</view>
		</view>
		<view v-else class="empty-container">
			<u-icon name="inbox" size="120" color="#cccccc"></u-icon>
			<text class="empty-text">暂无工序执行菜单</text>
			<text class="empty-desc">请联系管理员配置工序执行菜单</text>
			<button @click="refreshMenu" class="refresh-btn">刷新菜单</button>
		</view>



		<!-- 缓存信息显示 -->
		<view v-if="showCacheInfo" class="cache-info">
			<text class="cache-title">缓存信息</text>
			<text class="cache-text">数据来源：{{ dataSource }}</text>
			<text class="cache-text">缓存时间：{{ cacheTime }}</text>
			<text class="cache-text">菜单数量：{{ processMenuList.length }}</text>
		</view>
		<!-- <custom-tab-bar ref="customtab1" :current-index="currentIndex"  @changeTab="handleTabChange" v-if="customTabBar" /> -->

		<!-- 工序执行弹窗组件  -->
		<ProcessExecutionModal
			ref="processModal"
			v-model="showProcessModal"
			:menu-item="selectedMenuItem"
		/>

	</view>
</template>

<script>
	import { mapState } from 'vuex';
	import ProcessExecutionModal from '@/components/ProcessExecutionModal.vue';
	import menuMatcher from '@/utils/menuMatcher.js';

	export default {
		name: 'ProcessList',
		components: {
			ProcessExecutionModal
		},
		data() {
			return {
				loading: true,
				processMenuList: [],
				dataSource: '',
				cacheTime: '',
				showCacheInfo: false, // 开发时可设为 true 查看缓存信息
				showProcessModal: false, // 控制弹窗显示
				selectedMenuItem: {} // 选中的菜单项
			};
		},
		computed: {
			...mapState(['vuex_processMenuList'])
		},
		mounted() {
			console.log('工序执行页面组件已挂载');
			this.loadProcessMenu();
		},
		methods: {
			/**
			 * 加载工序执行菜单（优先从缓存读取，支持 isShow 过滤）
			 */
			async loadProcessMenu() {
				console.log('开始加载工序执行菜单...');
				this.loading = true;

				try {
					// 方法1: 优先从匹配后的本地存储读取
					const matchedMenu = uni.getStorageSync('matchedProcessMenus');
					if (matchedMenu && matchedMenu.length > 0) {
						console.log('✓ 从匹配后的本地存储加载工序执行菜单:', matchedMenu);
						// 过滤出 isShow 为 true 的菜单项
						this.processMenuList = this.filterVisibleProcessMenus(matchedMenu);
						this.dataSource = '匹配后缓存';
						this.cacheTime = this.formatCacheTime();
						this.loading = false;
						return;
					}

					// 方法2: 从 Vuex 读取
					if (this.vuex_processMenuList && this.vuex_processMenuList.length > 0) {
						console.log('✓ 从 Vuex 加载工序执行菜单:', this.vuex_processMenuList);
						// 过滤出 isShow 为 true 的菜单项
						this.processMenuList = this.filterVisibleProcessMenus(this.vuex_processMenuList);
						this.dataSource = 'Vuex 状态';
						this.cacheTime = this.formatCacheTime();
						this.loading = false;
						return;
					}

					// 方法3: 从旧版本缓存读取（兼容性）
					const cachedMenu = uni.getStorageSync('processMenuList');
					if (cachedMenu && cachedMenu.length > 0) {
						console.log('✓ 从旧版本缓存加载工序执行菜单:', cachedMenu);
						this.processMenuList = cachedMenu;
						this.dataSource = '旧版本缓存';
						this.cacheTime = this.formatCacheTime();
						this.loading = false;
						return;
					}

					// 方法4: 实时从 API 获取（备用方案）
					await this.loadFromAPI();

				} catch (error) {
					console.error('❌ 加载工序执行菜单失败:', error);
					this.processMenuList = [];
					this.dataSource = '加载失败';
				} finally {
					this.loading = false;
				}
			},

			/**
			 * 过滤出 isShow 为 true 的工序执行菜单项
			 * @param {Array} processMenuList - 工序执行菜单列表
			 * @returns {Array} 过滤后的菜单列表
			 */
			filterVisibleProcessMenus(processMenuList) {
				console.log('=== 开始过滤工序执行菜单 ===');
				console.log('输入的菜单数据:', processMenuList);

				if (!processMenuList || !Array.isArray(processMenuList)) {
					console.warn('输入数据无效，返回空数组');
					return [];
				}

				const filteredMenus = processMenuList.map((category, categoryIndex) => {
					console.log(`处理分类 ${categoryIndex + 1}: ${category.menuName} (isShow: ${category.isShow})`);

					if (!category.childList || !Array.isArray(category.childList)) {
						console.log(`  分类 ${category.menuName} 没有子菜单，直接返回`);
						return category;
					}

					// 过滤出 isShow 为 true 的子菜单项
					const visibleChildren = category.childList.filter((child, childIndex) => {
						const isVisible = child.isShow === true;
						console.log(`    子菜单 ${childIndex + 1}: ${child.menuName} (isShow: ${child.isShow}) -> ${isVisible ? '显示' : '隐藏'}`);
						return isVisible;
					});

					console.log(`  分类 ${category.menuName} 可见子菜单数量: ${visibleChildren.length}`);

					// 如果分类本身 isShow 为 true，显示分类，但只显示 isShow=true 的子菜单
					if (category.isShow === true) {
						console.log(`  ✓ 分类 ${category.menuName} 本身 isShow=true，将被保留（可见子菜单: ${visibleChildren.length} 个，总子菜单: ${category.childList.length} 个）`);

						// 只保留 isShow=true 的子菜单，严格按照权限控制显示
						return {
							...category,
							childList: visibleChildren // 只保留可见的子菜单
						};
					}

					// 如果分类 isShow 为 false 但有可见的子菜单项，也显示分类
					if (visibleChildren.length > 0) {
						console.log(`  ✓ 分类 ${category.menuName} 有可见子菜单，将被保留`);
						return {
							...category,
							childList: visibleChildren
						};
					}

					// 如果分类 isShow 为 false 且没有可见的子菜单项，返回 null（稍后会被过滤掉）
					console.log(`  ✗ 分类 ${category.menuName} 将被过滤掉`);
					return null;
				}).filter(category => category !== null); // 移除没有可见子菜单的分类

				console.log('过滤工序执行菜单完成:', {
					原始分类数: processMenuList.length,
					过滤后分类数: filteredMenus.length,
					可见菜单项总数: filteredMenus.reduce((total, category) => total + (category.childList?.length || 0), 0)
				});

				console.log('最终过滤结果:', filteredMenus);
				console.log('=== 工序执行菜单过滤完成 ===');

				return filteredMenus;
			},

			/**
			 * 从 API 实时加载工序执行菜单（使用菜单匹配器）
			 */
			async loadFromAPI() {
				console.log('从 API 实时加载工序执行菜单...');

				try {
					const apiMenuList = await this.$u.api.m8.findMenuList({
						parentFuncFlag: 'menu:m8:gongxvzhixin',
						clientFlag: this.vuex_config.menuConfig.CLIENT_APP,
					});

					if (apiMenuList && apiMenuList.length > 0) {
						console.log('✓ API 加载工序执行菜单成功:', apiMenuList.length, '项');

						// 使用菜单匹配器进行匹配
						const matchedProcessMenus = menuMatcher.matchProcessMenus(apiMenuList);

						// 过滤出 isShow 为 true 的菜单项
						this.processMenuList = this.filterVisibleProcessMenus(matchedProcessMenus);
						this.dataSource = 'API 实时匹配';
						this.cacheTime = this.formatCacheTime();

						// 更新缓存（保存匹配后的完整数据）
						uni.setStorageSync('matchedProcessMenus', matchedProcessMenus);
						uni.setStorageSync('processMenuList', apiMenuList); // 保留原始数据用于兼容
						this.$store.commit('setProcessMenuList', matchedProcessMenus);

						console.log('✓ 工序执行菜单匹配完成，可见菜单:', this.processMenuList.length, '个分类');

					} else {
						console.warn('⚠️ API 未返回工序执行菜单数据，尝试使用默认配置');
						this.loadDefaultProcessMenus();
					}

				} catch (error) {
					console.error('❌ API 加载工序执行菜单失败:', error);
					this.processMenuList = [];
					this.dataSource = 'API 错误';
					throw error;
				}
			},

			/**
			 * 加载默认工序执行菜单（从 menu.json 获取 isShow=true 的项）
			 */
			loadDefaultProcessMenus() {
				console.log('加载默认工序执行菜单（从 menu.json 获取 isShow=true 的项）');

				try {
					// 从 menu.json 中获取 isShow 为 true 的工序执行菜单项
					const defaultProcessMenus = menuMatcher.getDefaultProcessConfig();

					if (defaultProcessMenus && defaultProcessMenus.length > 0) {
						// 过滤出 isShow 为 true 的菜单项
						this.processMenuList = this.filterVisibleProcessMenus(defaultProcessMenus);
						this.dataSource = 'menu.json 默认';
						this.cacheTime = this.formatCacheTime();

						console.log('✓ 使用 menu.json 中的默认工序执行菜单:', this.processMenuList.length, '个分类');
					} else {
						console.warn('⚠️ menu.json 中没有默认工序执行菜单');
						this.processMenuList = [];
						this.dataSource = '无数据';
					}
				} catch (error) {
					console.error('加载默认工序执行菜单失败:', error);
					this.processMenuList = [];
					this.dataSource = '加载失败';
				}
			},

			/**
			 * 刷新菜单
			 */
			async refreshMenu() {
				console.log('手动刷新工序执行菜单...');

				// 清除所有相关缓存
				uni.removeStorageSync('processMenuList');
				uni.removeStorageSync('matchedProcessMenus');
				this.$store.commit('setProcessMenuList', []);

				// 重新加载
				await this.loadProcessMenu();

				uni.showToast({
					title: '菜单已刷新',
					icon: 'success'
				});
			},

			/**
			 * 导航到指定页面
			 */
			navigateToPage(item) {
				console.log('导航到工序执行页面:', item);

				// 检查是否需要弹窗显示
				const extendS2 = item?.extend?.extendS2;
				const modalExtendS2List = [
					'menu:m8:gx:kaigong',
					'menu:m8:gx:wangong',
					'menu:m8:gx:songjian'
				];

				if (modalExtendS2List.includes(extendS2)) {
					console.log('显示工序执行弹窗:', extendS2);
					this.selectedMenuItem = item;

					// 使用 $nextTick 确保 props 更新后再调用方法
					this.$nextTick(() => {
						if(this.vuex_config.deviceType === this.vuex_config.deviceTypePDA){
							this.$refs.processModal.showPopup();
						} else {
							this.$refs.processModal.search();
						}
					});
					return;
				}

				// 普通页面跳转
				if (!item.menuUrl) {
					uni.showToast({
						title: '页面路径未配置',
						icon: 'error'
					});
					return;
				}

				uni.navigateTo({
					url: item.menuUrl,
					success: () => {
						console.log('✓ 导航成功:', item.menuUrl);
					},
					fail: (error) => {
						console.error('❌ 导航失败:', error);
						uni.showToast({
							title: '页面跳转失败',
							icon: 'error'
						});
					}
				});
			},

			/**
			 * 格式化缓存时间
			 */
			formatCacheTime() {
				const now = new Date();
				return now.toLocaleString('zh-CN', {
					year: 'numeric',
					month: '2-digit',
					day: '2-digit',
					hour: '2-digit',
					minute: '2-digit',
					second: '2-digit'
				});
			}
		}
	};
</script>

<style scoped lang="scss">
	.cu-item {
		width: 41% !important;
		height: 90px;
	}

	.cuIcon-cardboardfill {
		margin-top: 20px !important;
	}

	.xm-title {
		border-radius: 20rpx;
		font-size: 36rpx;
		background-color: #fff;
		// background-image: url("/static/image/zfgs/index/<EMAIL>");
		// background-repeat: no-repeat;
		// background-size: 38rpx 42rpx;
		// background-position: 10rpx 10rpx;
		position: relative;

		.xm-title-p1 {
			color: #999999;
			font-size: 36rpx;
			line-height: 40rpx;
			margin-bottom: 8rpx;
		}

		.xm-title-p2 {
			color: #3d3d3d;
			line-height: 48rpx;
		}

		.xm-title-img {
			width: 38rpx;
			height: 42rpx;
			position: absolute;
			top: 0;
			right: 32rpx;
		}
	}

	.xm-item-1 {
		font-size: 42rpx;
		color: #3d3d3d;
		line-height: 40rpx;
		position: relative;
		margin-bottom: 26rpx;
	}

	.xm-item-1::after {
		content: "";
		display: inline-block;
		position: absolute;
		width: 32rpx;
		height: 4rpx;
		background-color: #bbbbbb;
		bottom: -16rpx;
		left: 0;
	}

	.xm-item {
		width: 200rpx;
	}

	.radius {
		border-radius: 20rpx;
	}

	.xm-item-2 {
		font-size: 34rpx;
		color: #999999;
	}

	// 添加 WarehouseList 中使用的其他样式类
	.wrap {
		background-color: #f8f8f8;
		/* 移除 min-height: 100vh，让父容器控制高度 */
		height: 100%;
		overflow-y: auto;
		overflow-x: hidden;
		/* 隐藏滚动条但保持滚动功能 */
		scrollbar-width: none; /* Firefox */
		-ms-overflow-style: none; /* IE 和 Edge */

		/* 当内容不足时不显示滚动条 */
		&::-webkit-scrollbar {
			display: none; /* Chrome, Safari, Opera */
		}
	}

	.cu-bar {
		padding: 20rpx;
		border-bottom: 1rpx solid #e0e0e0;
	}

	.action {
		display: flex;
		align-items: center;
	}

	.text-lg {
		font-size: 36rpx;
		margin-left: 15rpx;
	}

	.text-bold {
		font-weight: bold;
	}


	.solid-bottom {
		border-bottom: 1rpx solid #e0e0e0;
	}



	// 加载和空状态样式
	.loading-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		height: 60vh;

		.loading-text {
			margin-top: 30rpx;
			font-size: 28rpx;
			color: #666666;
		}
	}

	.empty-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		height: 60vh;

		.empty-text {
			margin-top: 30rpx;
			font-size: 32rpx;
			color: #333333;
			font-weight: bold;
		}

		.empty-desc {
			margin-top: 15rpx;
			font-size: 26rpx;
			color: #666666;
		}

		.refresh-btn {
			margin-top: 40rpx;
			padding: 20rpx 40rpx;
			background-color: #409eff;
			color: #ffffff;
			border: none;
			border-radius: 8rpx;
			font-size: 28rpx;
		}
	}

	.cache-info {
		margin-top: 40rpx;
		padding: 20rpx;
		background-color: #ffffff;
		border-radius: 16rpx;
		border: 1rpx solid #e0e0e0;

		.cache-title {
			display: block;
			font-size: 28rpx;
			font-weight: bold;
			color: #333333;
			margin-bottom: 15rpx;
		}

		.cache-text {
			display: block;
			font-size: 24rpx;
			color: #666666;
			margin-bottom: 8rpx;
		}
	}
</style>
