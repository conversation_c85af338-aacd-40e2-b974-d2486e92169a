<template>
	<view>
		<js-error mode="bottom" ref="jsError"></js-error>
		<view class="search">
			<u-search v-model="query['oper.operName']" :show-action="false" @search="loadData" placeholder="请输入工序搜索" ></u-search>
		</view>
		
		<scroll-view scroll-y="true" @scrolltolower="loadMore" :refresher-enabled="true" :scrolltolower-enabled="true"
			:refresher-triggered="triggered" @refresherrefresh="refresherrefresh" @scrolltoupper="refresherrefresh"
			class="scroll-view-class" :style="{ height: computedScrollViewHeight }">
			<view class="scroll-content" ref="scrollContent" style="
          display: flex;
          flex-direction: column;
          justify-content: space-between;
		  width: 100%;
        " :style="{ height: computedScrollViewHeight  }">
			<u-radio-group v-model="value">
				
				<!-- @click="xzgx(item)" -->
				<view v-for="(item,index) in list"   @click="addData(item,index)" class="cu-item shadow " style="position: relative;margin-bottom: 10px;width: 100%;" :key="item.id">
					<view class="cu-form-group"  style="display: flex;justify-content: space-between;">
						<view
							style=" display: inline-block;padding: 5px;background: #3E97B0;color: #fff;min-width: 40px;text-align: center;font-style: italic;font-weight: bold">
							{{ index + 1 }}
						</view>
						<!-- <view class="text-red text-bold"> {{ item.sortNum|| ""  }}</view> -->
						<view class="">
							<dictLabel style="margin-left: 10px;" :value="item.flowStatus" dict-type="M8_order_status">
							</dictLabel>
						</view>
					</view>
					<!-- <view class="cu-form-group">
						<view class="text-xl text-bold">{{ item.starttime || "" }} <text style="margin: 0 10px;">至</text>  {{ item.endtime || "" }}</view>
					  </view> -->
					<!-- <view class="cu-form-group">
						<view class="title">任务：</view>
						<view style="flex: 1;"> {{ item.taskCode|| ""  }}</view>
					</view> -->
					<view class="cu-form-group">
						<view class="title">工序：</view>
						<view style="flex: 1;">({{ item.sortNum || ''}}) {{ item.oper.operName || ""  }}</view>
						<u-radio class="rights" :name="index"></u-radio>
					</view>
					
				</view>
				</u-radio-group>

				<view v-if="list.length" class="loadmore" style="justify-self: flex-end">
					<u-loadmore :status="loadStatus"></u-loadmore>
				</view>
				<u-empty v-if="!list.length" style="height: 80vh;"></u-empty>
			</view>
		</scroll-view>

		<view>
			<movable-area class="movable-area">
				<movable-view class="movable-view" :x="x" :y="y" direction="all">
					<u-button size="mini" @click="toAdd" type="primary"
						style="width: 90px; height: 70px; color: #fff; font-size: 20px">确认
					</u-button>
				</movable-view>
			</movable-area>
		</view>
	</view>
</template>
<script>
	import {
		hasPermission
	} from '@/common/fire.js'
	import dictLabel from "@/components/dictLabel.vue";
	export default {
		components: {
			dictLabel,
		},
		data() {
			return {
				x: 650, //x坐标
				y: 650, //y坐标
				value: null,
				radioValue:{},
				model:{},
				flowListData: [], // 存储父组件传递的flowList数据
				companySelectList:[],
				show: false,
				smshow: false,
				focus: true,
				barCode: "",
				remarks: '',
				czData: {},
				show2: false,
				type: 'text',
				startTime: false, //控制日期显示
				endTime: false, //控制日期显示
				show: false,
				inputStyle: {
					fontSize: '34rpx'
				},
				filterIcon: "/static/image/filter.png",
				keyword: "",
				background: {
					backgroundColor: '#3E97B0',
				},
				list: [],
				itemContent: {},
				query: {
					pageNo: 1,
					pageSize: 5,
				},
				loadStatus: "loadmore",
				triggered: false,
				flag: false,
				scrollViewHeight: 0,
				headerHeight: 0,
				projStatus: this.$store.state.auth.projStatus,
			};
		},
		onShow() {
			let that = this
			that.query.pageNo = 1;
			// 只有当有父组件数据时才加载数据
			if (that.flowListData && that.flowListData.length > 0) {
				that.loadData();
			}
			// this.query.companyCode = this.vuex_company.companyCode || '';
			// this.query['company.companyName ']= this.vuex_company.companyName || '';
			// this.query.pageNo = 1;
			// this.loadData();

			// this.$u.api.ktnw.companyTreeData().then(res => {
			// 	this.companySelectList = res;
			// });
		},
		onLoad(e) {

			let that = this
			const eventChannel = this.getOpenerEventChannel();
			eventChannel.on('selectList', function(data) {
				console.log('data===selectList',data);
				// 接收父组件传递的完整数据
				that.model = data;
				that.flowListData = data.flowList || [];
				that.query.pageNo = 1;
				that.loadData();
				that.$forceUpdate()
			})

			// this.query['orderPic.picno'] = e.picno || ''
			// this.query['orderPic.picname'] = e.picname || ''
			// this.query.pageNo = 1;
			// this.loadData();
		},
		mounted() {
			// this.$refs.xmInfo.$on('child-mounted-done', () => {
			//   this.calculateScrollViewHeight();
			// });
			this.calculateScrollViewHeight();
		},
		computed: {
			computedScrollViewHeight() {
				return this.scrollViewHeight === 'auto' ? 'auto' : `${this.scrollViewHeight}px`;
			},
		},
		methods: {
			addData(item,index) {
				this.value = index
				this.radioValue = item
			},
			toAdd() {
				if (this.radioValue.flowStatus != undefined && this.radioValue.flowStatus) {
					this.xzgx(this.radioValue)
				} else {
					this.$u.toast("请选择至少一条数据！");
					return;
				}
			},
			xzgx(item){
				let that = this
				if(item.taskStatus == '3'){
					uni.showModal({
						title: '提示',
						content: '当前任务已完成,是否继续加工？',
						confirmColor: '#F54E40',
						success: (res) => {
							if (res.confirm) {
								
								let data = {
									...that.model,
									oper:item.oper,
									task:item
								}
								
								uni.navigateTo({
									url: '/pages/m8/gongxu/beginForm',
									success: function(resq) {
										resq.eventChannel.emit('beginForm', data)
									}
								})
								// uni.$emit('xzgx',item);
								// uni.navigateBack({
								// 	delta: 1,
								// });
								return false;
							}
						}	
					})
				}else if(item.taskStatus == '2'){
					uni.showModal({
						title: '提示',
						content: '当前任务正在其它机床加工,是否继续？',
						confirmColor: '#F54E40',
						success: (res) => {
							if (res.confirm) {
								let data = {
									...that.model,
									oper:item.oper,
									task:item
								}
								
								uni.navigateTo({
									url: '/pages/m8/gongxu/beginForm',
									success: function(resq) {
										resq.eventChannel.emit('beginForm', data)
									}
								})
								// uni.$emit('xzgx',item);
								// uni.navigateBack({
								// 	delta: 1,
								// });
								return false;
							}
						}
					})
				}else if(item.taskStatus == '9'){
					uni.showModal({
						title: '提示',
						content: '当前任务已暂停,是否继续加工？',
						confirmColor: '#F54E40',
						success: (res) => {
							if (res.confirm) {
								let data = {
									...that.model,
									oper:item.oper,
									task:item
								}
								
								uni.navigateTo({
									url: '/pages/m8/gongxu/beginForm',
									success: function(resq) {
										resq.eventChannel.emit('beginForm', data)
									}
								})
								// uni.$emit('xzgx',item);
								// uni.navigateBack({
								// 	delta: 1,
								// });
								return false;
							}
						}	
					})
				}else{
					let data = {
						...that.model,
						flowId: item.id,
						flow:item
					}
					uni.navigateTo({
						url: '/pages/m8/gongxu/beginForm',
						success: function(resq) {
							resq.eventChannel.emit('beginForm', data)
						}
					})	
				}
				
			},
			submit() {
				setTimeout(() => {
					this.query.pageNo = 1;
					this.loadData();
					this.show = false
				}, 100);
			},
			
			// this.$forceUpdate();
			reset() {
				this.list = [];
				this.query = {
					pageNo: 1,
					pageSize: 20,
				};
				this.loadData();
				this.show = false
			},
			
			startConfirm(e) {
				this.query.arrDate_gte = e.year + '-' + e.month + '-' + e.day;
			},
			endConfirm(e) {
				this.query.arrDate_lte = e.year + '-' + e.month + '-' + e.day;
			},
			customBack() {
				// 跳转到工作台页面
				// uni.navigateTo({
				//   url: "/pages/zfgs/index/index/index?item=" + JSON.stringify(this.itemContent),
				// });
				uni.navigateBack({
					delta: 1,
				});
			},
			async calculateScrollViewHeight() {
				try {
					// 等待头部高度计算完成
					// await new Promise((resolve) => {
					//   this.$nextTick(() => {
					//     this.headerHeight = this.$refs.xmInfo.$refs['u-sticky'].height + this.$refs.xmInfo.$refs['u-sticky'].h5NavHeight + this.$refs.navbar.navbarHeight;
					//     resolve();
					//   });
					// });
					this.headerHeight = 52
					// 计算tabBar高度
					if (this.flag) {
						this.tabBarHeight = 60; // 假设tabbar高度为50px，实际应根据实际情况获取
					} else {
						this.tabBarHeight = 0;
					}
					// 获取屏幕高度
					const systemInfo = uni.getSystemInfoSync();
					const screenHeight = systemInfo.windowHeight;
					// 计算scroll-view高度
					this.scrollViewHeight = screenHeight - (this.headerHeight + this.tabBarHeight);
				} catch (error) {
					console.error('Error while calculating ScrollView height:', error);
				}
			},

			loadMore() {
				this.loadStatus = "loading";
				setTimeout(() => {
					this.query.pageNo += 1;
					this.loadData("add");
				}, 100);
			},
			loadData(type) {
				// 直接从父组件读取数据还原给list
				if (this.flowListData && this.flowListData.length > 0) {
					var data = this.flowListData;

					// 根据搜索条件过滤数据
					if (this.query['oper.operName']) {
						data = data.filter(item => {
							return item.oper && item.oper.operName &&
								   item.oper.operName.includes(this.query['oper.operName']);
						});
					}

					// 分页处理
					const pageSize = this.query.pageSize || 5;
					const pageNo = this.query.pageNo || 1;
					const startIndex = (pageNo - 1) * pageSize;
					const endIndex = startIndex + pageSize;
					const pagedData = data.slice(startIndex, endIndex);

					// 设置加载状态
					if (pagedData.length < pageSize || endIndex >= data.length) {
						this.loadStatus = "nomore";
					} else {
						this.loadStatus = "loadmore";
					}

					// 更新列表数据
					if (type == "add") {
						for (var i = 0; i < pagedData.length; i++) {
							this.list.push(pagedData[i]);
						}
					} else {
						this.list = pagedData;
					}
				} else {
					// 如果没有父组件数据，设置为空列表
					this.list = [];
					this.loadStatus = "nomore";
				}
			},
			refresherrefresh() {
				this.triggered = true;
				this.query.pageNo = 1;
				this.loadData();
				const scrollTop = 0
				uni.pageScrollTo({
					scrollTop,
					duration: 0,
				});
				setTimeout(() => {
					this.triggered = false;
				}, 500);
			},
		},
		// beforeDestroy() {
		// 	// 清理事件监听器
		// 	try {
		// 		this.$refs.xmInfo.$off('child-mounted-done');
		// 	} catch (e) {
		// 		console.log('Error while unbinding event:', e);
		// 	}
		// }
	};
</script>
<style lang="scss" scoped>
	$all_width: 96rpx;
	$all_height: 96rpx;

	.scroll-view-class {
		/* 隐藏滚动条但保持滚动功能 */
		scrollbar-width: none; /* Firefox */
		-ms-overflow-style: none; /* IE 和 Edge */

		/* 当内容不足时不显示滚动条 */
		&::-webkit-scrollbar {
			display: none; /* Chrome, Safari, Opera */
		}
	}

	.movable-area {
		z-index: 999;
		height: 97vh;
		width: 650rpx;
		// max-width: 1152rpx;
		// 固定在右边
		top: -20rpx;
		position: fixed;
		right: $all_width;
		// 固定在右下角
		pointer-events: none; //此处要加，鼠标事件可以渗透
	
		.movable-view {
			width: $all_width;
			height: $all_height;
			pointer-events: auto; //恢复鼠标事件
	
			image {
				width: $all_width;
				height: $all_height;
			}
		}
	}
	/deep/.u-radio__icon-wrap[data-v-da6758f0] {
		width: 30px !important;
		height:30px !important;
	}
	
	.text-border{
		border:2px solid  rgb(170, 170, 170); 
		border-radius: 5px;
		position: relative;
		padding-top: 20rpx;
		margin-top: 40rpx;
		padding-left: 20rpx;
		.text{
			position: absolute;
			background-color: #fff;
			padding: 10rpx 10rpx;
			top: -25rpx;
			right: 30rpx;
		}
	}
	.custom-style{
		// background-color: rgba(25, 190, 107, 0.7);
		
	}	
	.text-xxl{
		font-size:60rpx;
	}
	.cu-bar {
		min-height: 60px;
	}
	
	.cu-btn {
		font-size: 16px;
		height: 40px;
	}
	
	.foot {
		background: #fff;
	}
	
	.cu-item {
		flex-wrap: wrap;
		padding: 8px 15px !important;
	}
	
	.title {
		width: 200rpx;
	}
	
	.cu-modal-slot {
		margin: 42rpx 32rpx 20rpx 32rpx;
		height: 100vh;
		position: relative;
	}
	
	.cu-modal-header {
		font-size: 38rpx;
		color: #3d3d3d;
		line-height: 40rpx;
		text-align: left;
		font-weight: 500;
	}
	
	.button {
		font-size: 32rpx;
		color: #666666;
		line-height: 40rpx;
		padding: 12rpx 40rpx;
		margin-bottom: 20rpx;
		background: #e2e2e2;
		border-radius: 180rpx;
	}

	.button:hover {
		background: #3e97b0;
		color: #ffffff;
	}

	.cu-modal-search-title {
		margin: 32rpx 0rpx 20rpx 0rpx;
		text-align: left;
		font-size: 32rpx;
		color: #999999;
		line-height: 40rpx;
	}

	.cu-dialog {
		background: #ffffff;
	}

	.cu-modal-footer {
		padding: 32rpx 32rpx;
		width: 100%;

		.cu-btn {
			width: 50%;
		}
	}

	.bg-confirm {
		background: #3e97b0;
		color: #ffffff;
	}
</style>