/**
 * 退出登录辅助工具
 * 用于清除所有缓存数据和状态
 */
import store from '@/store';
import menuMatcher from '@/utils/menuMatcher.js';

class LogoutHelper {
	/**
	 * 执行完整的退出登录流程
	 * @param {Object} options 配置选项
	 * @param {boolean} options.callLogoutAPI 是否调用后端logout API，默认true
	 * @param {boolean} options.showToast 是否显示提示信息，默认true
	 * @param {string} options.redirectUrl 退出后跳转的页面，默认登录页
	 * @param {number} options.delay 跳转延迟时间(ms)，默认500
	 */
	async logout(options = {}) {
		const {
			callLogoutAPI = true,
			showToast = true,
			redirectUrl = '/pages/sys/index/login/index',
			delay = 500
		} = options;

		console.log('开始执行退出登录流程...');

		try {
			// 1. 调用后端logout API（如果需要）
			if (callLogoutAPI) {
				try {
					// 使用uni.request直接调用，避免依赖$u
					const logoutResult = await new Promise((resolve, reject) => {
						uni.request({
							url: store.state.vuex_config.baseUrl + '/logout',
							method: 'POST',
							header: {
								'Content-Type': 'application/json'
							},
							success: (res) => {
								resolve(res.data);
							},
							fail: (error) => {
								reject(error);
							}
						});
					});

					if (showToast && logoutResult.message) {
						uni.showToast({
							title: logoutResult.message,
							icon: 'none'
						});
					}
					console.log('✓ 后端logout API调用成功');
				} catch (error) {
					console.warn('⚠️ 后端logout API调用失败，继续执行本地清理:', error);
					if (showToast) {
						uni.showToast({
							title: '退出登录中...',
							icon: 'none'
						});
					}
				}
			}

			// 2. 清除Vuex状态和本地存储
			this.clearAllCache();

			// 3. 清除菜单相关缓存
			this.clearMenuCache();

			// 4. 清除其他可能的本地缓存
			this.clearOtherCache();

			console.log('✓ 退出登录流程执行完成');

			// 5. 跳转到登录页面
			if (redirectUrl) {
				setTimeout(() => {
					uni.reLaunch({
						url: redirectUrl
					});
				}, delay);
			}

			return true;

		} catch (error) {
			console.error('❌ 退出登录流程执行失败:', error);
			if (showToast) {
				uni.showToast({
					title: '退出登录失败',
					icon: 'none'
				});
			}
			return false;
		}
	}

	/**
	 * 清除Vuex状态和主要本地存储
	 */
	clearAllCache() {
		try {
			// 调用store中的clearAllCache mutation
			store.commit('clearAllCache');
			console.log('✓ Vuex状态和主要本地存储已清除');
		} catch (error) {
			console.error('❌ 清除Vuex状态失败:', error);
		}
	}

	/**
	 * 清除菜单相关缓存
	 */
	clearMenuCache() {
		try {
			// 使用menuMatcher清除菜单缓存
			menuMatcher.clearLocalStorage();
			console.log('✓ 菜单相关缓存已清除');
		} catch (error) {
			console.error('❌ 清除菜单缓存失败:', error);
		}
	}

	/**
	 * 清除其他可能的本地缓存
	 */
	clearOtherCache() {
		try {
			// 清除可能存在的其他缓存项
			const otherCacheKeys = [
				'userInfo',
				'loginInfo',
				'appConfig',
				'deviceInfo',
				'lastLoginTime',
				'autoLogin',
				'rememberPassword',
				'selectedCompany',
				'workstation',
				'department'
			];

			otherCacheKeys.forEach(key => {
				try {
					uni.removeStorageSync(key);
				} catch (e) {
					// 忽略不存在的key
				}
			});

			console.log('✓ 其他缓存项已清除');
		} catch (error) {
			console.error('❌ 清除其他缓存失败:', error);
		}
	}

	/**
	 * 仅清除缓存，不调用API，不跳转页面
	 * 适用于需要手动控制流程的场景
	 */
	clearCacheOnly() {
		console.log('开始清除缓存数据...');
		
		this.clearAllCache();
		this.clearMenuCache();
		this.clearOtherCache();
		
		console.log('✓ 缓存数据清除完成');
	}

	/**
	 * 检查是否有残留的缓存数据
	 * 用于调试和验证清理效果
	 */
	checkRemainingCache() {
		console.log('=== 检查残留缓存数据 ===');
		
		// 检查主要的存储项
		const mainKeys = ['lifeData', 'HBusername', 'HBpassword', 'loaclOpenId', 'wxavatarUrl'];
		const menuKeys = ['matchedTabBarMenus', 'matchedProcessMenus', 'matchedWarehouseMenus', 'matchedWorkshopMenus', 'matchedMachineMenus'];
		
		let hasRemaining = false;
		
		[...mainKeys, ...menuKeys].forEach(key => {
			try {
				const value = uni.getStorageSync(key);
				if (value !== '' && value !== null && value !== undefined) {
					console.warn(`⚠️ 发现残留缓存: ${key}`, value);
					hasRemaining = true;
				}
			} catch (e) {
				// 忽略读取错误
			}
		});
		
		// 检查Vuex状态
		const vuexState = store.state;
		if (vuexState.vuex_token && vuexState.vuex_token !== '') {
			console.warn('⚠️ Vuex中仍有token:', vuexState.vuex_token);
			hasRemaining = true;
		}
		
		if (vuexState.vuex_user && vuexState.vuex_user.userName !== 'JeeSite') {
			console.warn('⚠️ Vuex中仍有用户信息:', vuexState.vuex_user);
			hasRemaining = true;
		}
		
		if (!hasRemaining) {
			console.log('✓ 未发现残留缓存数据');
		}
		
		console.log('=== 缓存检查完成 ===');
		return !hasRemaining;
	}
}

// 创建单例实例
const logoutHelper = new LogoutHelper();

export default logoutHelper;
