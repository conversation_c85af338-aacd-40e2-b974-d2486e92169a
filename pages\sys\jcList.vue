<template>
	<view class="wrap ">
		<!-- <js-lang title="home.title" :showBtn="true"></js-lang> -->
		<!-- <u-swiper :height="300" :list="imgList" :title="false" @click="imgListClick"></u-swiper> -->
		<view v-for="res in menuList2" :key="res.menuCode">
			<view class="cu-bar bg-white solid-bottom">
				<view class="action ">
					<u-icon name="/static/image/zfgs/index/icon@2x(6).png" size="80"></u-icon>
					<text class="text-lg  text-bold " style="font-size: 42rpx;">{{res.menuName}}</text>
				</view>
			</view>
			<view class="flex margin-sm flex-wrap justify-between u-skeleton">
				<view class="flex bg-white padding radius " v-for="item in res.childList"
					:key="item.menuName" @click="navTo(item)" style="margin-bottom: 15rpx;">
					<view class="xm-item u-skeleton-rect">
						<view class="xm-item-1">{{item.menuName}}</view>
						<view class="xm-item-2">{{item.menuTitle}}</view>
					</view>
					<image style="width: 86rpx; height: 86rpx" :src="item.menuIcon" mode="aspectFit"
						class="u-skeleton-circle" />
				</view>
			</view>
			
			<!-- <view class="margin-sm ">
				<view class="flex bg-white padding radius justify-between" v-for="item in res.childList"
					:key="item.menuName" @click="navTo(item.url)" style="margin-bottom: 20rpx;">
					<view class="xm-item u-skeleton-rect">
						<view class="xm-item-1">{{item.menuName}}</view>
						<view class="xm-item-2">{{item.menuTitle}}</view>
					</view>
					<image style="width: 86rpx; height: 86rpx" :src="item.menuIcon" mode="aspectFit"
						class="u-skeleton-circle" />
				</view>
			</view> -->
		</view>
		<view style="height: 1px"></view>
		<view style="height: 108rpx"></view>
		<repCheck ref="repCheckRef"></repCheck>
		<!-- <custom-tab-bar ref="customtab1" :current-index="currentIndex"  @changeTab="handleTabChange" v-if="customTabBar" /> -->

	</view>
</template>

<script>
	import {
		mapState
	} from 'vuex';
	import checkVersion from "@/pages/lq-upgrade/checkVersion.js";
	import customTabBar from '@/pages/m8/componetns/customTabBar.vue';
	import repCheck from "@/pages/m8/repCheck/index.vue"
	export default {
		components: {
			customTabBar,
			repCheck
		},
		data() {
			return {
				currentIndex:1,
				customTabBar:true,
				model:{},
				selectList:[],
				stringPermissions: [],
				todoCount: 0,
				menuList2: [
					{
						menuName:'业务',
						childList:[
							// {
							// 	menuName:'机床',
							// 	menuTitle:'机床',
							// 	extend:{
							// 		extendS2:'menue:ktnw:qtsj'
							// 	},
							// 	menuIcon: '/static/image/zfgs/jc_select.png',
							// 	url: '/pages/m8/machine/list',
							// },
							{
								menuName:'送检',
								menuTitle:'数铣送检',
								menuIcon: '/static/image/tzsj.png',
								url: '',
								funcType:'repCheck'
							},
						]
					},
				],
				menuList1:[],
			}
		},
		onShow() {			
			this.customTabBar = false ;
			setTimeout(()=>{
			    this.customTabBar = true ;
			},100)
		},
		created() {
			 
		},
		onLoad() {
			this.upgrade();
			var _this = this;
			// this.$u.api.menuTree().then(res => {
			// 	if (res.length > 0) {
			// 		res.forEach(item => {
			// 			if ('系统管理' == item.menuName) {
			// 				item.childList.forEach(item2 => {
			// 					if ('移动端管理' == item2.menuName) {
			// 						// this.showMenu(item2.childList);
			// 						item2.childList.forEach(item3 => {
			// 							if ('APP菜单' == item3.menuName) {
			// 								this.showMenu(item3.childList);
			// 							}
			// 						})
			// 					}
			// 				})

			// 			}
			// 		})

			// 	}
			// });
			// this.$u.api.authInfo().then(res => {
			// 	this.stringPermissions=(res==null || res.stringPermissions==null)?[]:res.stringPermissions;
			// });
		},
		methods: {
			handleTabChange(pagePath) {
			  // 切换页面逻辑，可使用uni.navigateTo等API
			  uni.reLaunch({ url: pagePath });
			},
			//检查版本更新
			upgrade() {
			  // #ifdef APP-PLUS
			  this.$u.api.upgradeCheck().then((res) => {
			    let url = res.data.apkUrl
			      ? res.data.apkUrl
			      : this.vuex_config.xtUrl + res.data.xtUrl;
			    if (res.result == "true") {
			      checkVersion({
			        name: res.data.upTitle, //最新版本名称
			        code: res.data.upVersion, //最新版本号
			        content: `${res.data.upContent}`, //更新内容
			        url, //下载链接
			        // forceUpdate: true, //是否强制升级
			        forceUpdate: res.data.upType == '3'?true:false, //是否强制升级
			      });
			    }
			  });
			  // #endif
			},
			//显示菜单
			showMenu(list) {
				this.menuList1 = list
				this.menuList.forEach(item => {
					this.menuList1.forEach(res => {
						res.childList.forEach(req => {
							if (req.extend?.extendS2 == item.extend?.extendS2 ) {
								req.menuIcon = item.menuIcon;
								req.url = item.url;
							}
						})

					})
				})
			},
			async selectConfirm() {
				await this.$u.api.ktnw.switchCorp({
						companyCode: this.model.companyCode
					})
					.then(res => {
						this.$u.vuex('vuex_company', this.model);
					});
			},
			navTo(item) {
				console.log('url',item)
				if(item.funcType == 'repCheck') {
					console.log('repCheck',this.$refs)

					if(this.vuex_config.deviceType === this.vuex_config.deviceTypePDA){
						this.$refs.repCheckRef.showPopup();
						return;
					} else {
						this.$refs.repCheckRef.search();
					}

					
					
					
					// search
				};
				
				uni.navigateTo({
					url: item.url
				});
			},
			itemClick(index) {
				console.log(index);
			}
		}
	}
</script>

<style scoped lang="scss">
	.cu-item {
		width: 41% !important;
		height: 90px;
	}

	.cuIcon-cardboardfill {
		margin-top: 20px !important;
	}

	.xm-title {
		border-radius: 20rpx;
		font-size: 36rpx;
		background-color: #fff;
		// background-image: url("/static/image/zfgs/index/<EMAIL>");
		// background-repeat: no-repeat;
		// background-size: 38rpx 42rpx;
		// background-position: 10rpx 10rpx;
		position: relative;

		.xm-title-p1 {
			color: #999999;
			font-size: 36rpx;
			line-height: 40rpx;
			margin-bottom: 8rpx;
		}

		.xm-title-p2 {
			color: #3d3d3d;
			line-height: 48rpx;
		}

		.xm-title-img {
			width: 38rpx;
			height: 42rpx;
			position: absolute;
			top: 0;
			right: 32rpx;
		}
	}

	.xm-item-1 {
		font-size: 42rpx;
		color: #3d3d3d;
		line-height: 40rpx;
		position: relative;
		margin-bottom: 26rpx;
	}

	.xm-item-1::after {
		content: "";
		display: inline-block;
		position: absolute;
		width: 32rpx;
		height: 4rpx;
		background-color: #bbbbbb;
		bottom: -16rpx;
		left: 0;
	}

	.xm-item {
		width: 200rpx;
	}

	.radius {
		border-radius: 20rpx;
	}

	.xm-item-2 {
		font-size: 34rpx;
		color: #999999;
	}
</style>