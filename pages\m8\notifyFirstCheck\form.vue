<template>
	<view class="wrap">
		<js-error mode="bottom" ref="jsError"></js-error>
		<!-- <view class="cu-bar search" style="padding: 10px">
			<u-search v-model="barCode" ref="uSearch" :focus="focus" placeholder="请先聚焦后扫描" :show-action="false"
				@search="confirm"></u-search>
			<view style="margin-left: 10px; display: flex; flex-direction: column">
				<u-icon @click="search" name="scan" size="50"></u-icon>
			</view>
		</view> -->
		<view style="background-color: #fff;">
			<u-form class="form bg-white" :model="model" ref="uForm" label-position="left" style="padding: 0 10px;">
			
				<!-- <u-form-item label="物料名称:" prop="invName" label-width="180" :label-style="{ 'font-weight': 'bold' }" required>
					<view @click="xzsp" style="display: flex;justify-content: space-between;width: 100%;font-size: 30rpx;">
						<view v-if="model.invName" style="margin-right: 10rpx;">{{ model.invName }}</view>
						<view v-if="!model.invName" style="margin-right: 10rpx;color: #eee;">请扫码或选择</view>
						<u-icon name="arrow-right"></u-icon>
					</view>
				</u-form-item> -->
				
				<u-form-item label="机床:" prop="viewCode" label-width="180" :label-style="{ 'font-weight': 'bold' }" >
					<view style="font-size: 30rpx;">{{ model.machineName || "" }}</view>
				</u-form-item>
				<u-form-item label="图名:" prop="viewCode" label-width="180" :label-style="{ 'font-weight': 'bold' }" >
					<view style="font-size: 30rpx;">{{ model.picname || "" }}</view>
				</u-form-item>
				<u-form-item label="图号:" prop="viewCode" label-width="180" :label-style="{ 'font-weight': 'bold' }" >
					<view style="font-size: 30rpx;">{{ model.picno || "" }}</view>
				</u-form-item>
				<u-form-item label="负责人:" prop="userName" label-width="180"  :label-style="{ 'font-weight': 'bold' }" >
					<view style="font-size: 30rpx;">{{ model['user.userName'] || "" }}</view>
				</u-form-item>
				
				<u-form-item label="预约时间:" prop="remarks" label-width="200" required
					:label-style="{ 'font-weight': 'bold', }">
					<u-input placeholder="请选择首检预约时间" v-model="model['machine.prefirstCheckDate']" type="select" class="input-align" :select-open="startTime"
								@click="startTime = true"></u-input>
				</u-form-item>
				<u-picker mode="time" :params="params" v-model="startTime" @confirm="startConfirm"></u-picker>
				<!-- <u-form-item label="实际加工时长:" prop="sjjgtime" label-width="240"  required
					:label-style="{ 'font-weight': 'bold', }">
					<u-input v-model="model.sjjgtime" type="number" placeholder="请输入"  clearable />
				</u-form-item>
				<u-form-item label="调试-说明:" prop="remarks" label-width="240" 
					:label-style="{ 'font-weight': 'bold', }">
					<u-input v-model="model.remarks"  placeholder="请输入"  clearable />
				</u-form-item> -->

			</u-form>
		</view>
		
		
		<!-- <view>
			<movable-area class="movable-area1">
				<movable-view class="movable-view" :x="x" :y="y" direction="all">
					<u-button size="mini" @click="search" type="success"
						style="width: 90px; height: 70px; color: #fff; ">
						<view style="font-size: 16px;display: flex; flex-direction: column;text-align: center;">
							<u-icon style="margin: auto;" name="/static/jeesite/sm1.png" size="80"></u-icon>
							<view >扫一扫</view>
						</view>
					</u-button>
					
				</movable-view>
			</movable-area>
		</view> -->

		<view>
			<movable-area class="movable-area">
				<movable-view class="movable-view" :x="x" :y="y" direction="all">
					<u-button size="mini" @click="submit" type="primary"
						style="width: 90px; height: 70px; color: #fff; font-size: 20px">确认
					</u-button>
				</movable-view>
			</movable-area>
		</view>
	</view>
</template>

<script>
	import {
		mapState
	} from 'vuex';
	export default {
		data() {
			return {
				params:{
					year: true,
					month: true,
					day: true,
					hour: true,
					minute: true,
					// second: true
				},
				startTime: false, //控制日期显示
				sjshow:false,
				stockListHeight: 0,
				whSelectList:[],
				barCode: "", //AA01|01GD02001000I010|SPHC 2.0*262*C|Z11106202310301-4231031124239004|001|3380|1|202311011010|0AAA||202310301-4||9120963|11106||||||
				showflag: 0,
				model: {
					user:{},
				},
				userSelectList:[],
				xmflag: false,
				iqty: "",
				cposCodeAndName: "",
				cposcode: "", //货位号
				flag: false,
				iqtyfocus: false,
				focus: true,
				x: 650, //x坐标
				y: 650, //y坐标
			};
		},
		onLoad(params) {
			let that = this
			const eventChannel = this.getOpenerEventChannel();
			eventChannel.on('notifyFirstCheck', function(data) {
				console.log(data,'data========');
				that.model = {
					picId:data.orderPic.id,
					picno:data.orderPic.picno,
					picname:data.orderPic.picname,
					"user.userCode":data.user.userCode,
					"user.userName":data.user.userName,
					// user:data.user,
					machineId:data.machineId,
					machineName:data.machine.name,
					'task.taskCode':data.task.taskCode
					// task:{
					// 	taskCode:data.task.taskCode
					// }
				}
				
				that.model['machine.prefirstCheckDate'] = that.getCurrentDateTime()
				that.$forceUpdate()
			})
			
			
			var _self = this;
			uni.getSystemInfo({
				success: (e) => {
					// resu 可以获取当前屏幕的高度
					_self.stockListHeight = e.windowHeight - uni.upx2px(160);
				},
				fail: (res) => {},
			});
			
		},
		watch: {},
		onShow() {
			this.$u.api.m8.officeTreeData({
				isLoadUser:true,
				isAll:true,
				ywType:'1',
			}).then(res => {
				this.userSelectList = res;
			});
		},
		onHide() {
		
		},
		onUnload() {
		},
		onReady() {},
		computed: {
			...mapState(['vuex_basWare'])
		},
		methods: {
			getCurrentDateTime() {
			  const now = new Date();
			  
			  const year = now.getFullYear();
			  const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从0开始，要+1
			  const day = String(now.getDate()).padStart(2, '0');
			  const hours = String(now.getHours()).padStart(2, '0');
			  const minutes = String(now.getMinutes()).padStart(2, '0');
			  
			  return `${year}-${month}-${day} ${hours}:${minutes}`;
			},
			startConfirm(e) {
				// + ":" + e.second
				this.model['machine.prefirstCheckDate'] = e.year + "-" + e.month + "-" + e.day + " " + e.hour+ ":" + e.minute
			},
			replaceInput(e) {
				console.log(e);
				var that = this
				e = e.match(/^\d*(\.?\d{0,2})/g)[0]
				this.$nextTick(() => {
					that.model.iqty = e
				})
			
			},
			/** 发生声音*/
			sendMp3(name) {
				console.log("=====testClick=====");
				let src = '/static/jeesite/' + name + '.mp3';
				//实例化声音  
				const Audio = uni.createInnerAudioContext();
				Audio.autoplay = true;
				Audio.src = src; //音频地址  
				Audio.play(); //执行播放  
				Audio.onError((res) => {
					console.log(res.errMsg);
					console.log(res.errCode);
				});
				Audio.onPause(function() {
					console.log('end');
					Audio.destroy();
				});
			},
			selectConfirm() {
				this.model.afterPos = ''
				this.model.afterPosName = ''
				this.$forceUpdate()
			},
			xzsp(){
				uni.navigateTo({
					url: "/pages/m8/ck/xzspList",
				});
			},
			BroadcastScanningToObtainData(res) {
				//获取扫描到的条形码
				let barcode = res.code
				
				//判断条形码长度是否大于3
				if(barcode.length > 3){
					//去除换行符
					let newString = barcode.replace('\n;', '');
					
					this.barCode = newString;
					this.confirm()
					//将换行符分割成数组
					// const allItems = newString.split('\n');
					// 	//遍历数组，将每一项添加到arr中
					// 	for(let i = 0;i<allItems.length;i++){
					// 		this.arr.push({
					// 			"content":allItems[i],
					// 			"remarks":this.remarks
					// 		})
					// 	}
				}
			},
			confirm() {
				let _that = this;
				_that.focus = false
				let POSITIONPrefix = _that.vuex_config.POSITIONPrefix;
				let INVPrefix = _that.vuex_config.INVPrefix;
				let bar = encodeURIComponent(this.barCode)
				if (bar.indexOf(POSITIONPrefix) != -1) {
					this.$u.api.m8.getBarInfo({
						barCode: bar,
					}).then((res) => {
						if(res.result == 'true' && !res.errorMsg){
							_that.sendMp3('cg');
							this.model.posCode = res.basPos.posCode
							this.model.posName = res.basPos.posName
							this.$forceUpdate()
						}else{
							_that.sendMp3('sb');
							let message = res.message || res.errorMsg || ''
							_that.$refs.jsError.showError("", message, "error");
						}
						this.barCode = ''
					})
				}else if(bar.indexOf(INVPrefix) != -1){
					this.$u.api.m8.getBarInfo({
						barCode: bar,
					}).then((res) => {
						if(res.result == 'true' && !res.errorMsg){
							_that.sendMp3('cg');
							this.model.invCode = res.basInv.invCode
							this.model.viewCode = res.basInv.viewCode
							this.model.invName = res.basInv.invName
							this.$forceUpdate()
						}else{
							_that.sendMp3('sb');
							let message = res.message || res.errorMsg || ''
							_that.$refs.jsError.showError("", message, "error");
						}
						this.barCode = ''
					})
				}else{
					_that.sendMp3('bcz');
					_that.$refs.jsError.showError("", "请扫描正确的货位码或存货", "error");
					setTimeout(() => {
						_that.focus = true;
						this.barCode = ''
					}, 500)
				}
			},
			search() {
				var _that = this;
				_that.focus = false
				uni.scanCode({
					scanType: ["barCode", "qrCode"],
					// onlyFromCamera: true,
					success: function(res) {
						_that.barCode = res.result;
						_that.confirm()
					},
				});
			},
			async submit() {
				// if (!this.model.userCode) {
				// 	this.$refs.jsError.showError("", "请先选择负责人", "error");
				// 	return;
				// }
				
				if (!this.model['machine.prefirstCheckDate']) {
					this.$refs.jsError.showError("", "请先选择首检预约时间", "error");
					return;
				}
				
				this.$u.api.m8.notifyFirstCheck(this.model).then((res) => {
					if (res.result == "true") {
						this.$u.toast(res.message);
						setTimeout(()=>{
							uni.navigateBack({
								delta: 1
							})
						},500)
					} else {
						this.$refs.jsError.showError("", res.message, "error");
				
					}
				});
				
			},
		},
	};
</script>
<style lang="scss">
	$all_width: 96rpx;
	$all_height: 96rpx;

	.movable-area2 {
		z-index: 999;
		height: 97vh;
		width: 650rpx;
		position: fixed;
		right: $all_width;
		top: -($all_height + 220rpx);
		pointer-events: none; //此处要加，鼠标事件可以渗透

		.movable-view {
			width: $all_width;
			height: $all_height;
			pointer-events: auto; //恢复鼠标事件

			image {
				width: $all_width;
				height: $all_height;
			}
		}
	}

	.movable-area1 {
		z-index: 888;
		height: 97vh;
		width: 650rpx;
		position: fixed;
		right: $all_width;
		top: -($all_height + 70rpx);
		pointer-events: none; //此处要加，鼠标事件可以渗透

		.movable-view {
			width: $all_width;
			height: $all_height;
			pointer-events: auto; //恢复鼠标事件

			image {
				width: $all_width;
				height: $all_height;
			}
		}
	}

	.movable-area {
		z-index: 999;
		height: 97vh;
		width: 650rpx;
		// max-width: 1152rpx;
		// 固定在右边
		top: -20rpx;
		position: fixed;
		right: $all_width;
		// 固定在右下角
		pointer-events: none; //此处要加，鼠标事件可以渗透

		.movable-view {
			width: $all_width;
			height: $all_height;
			pointer-events: auto; //恢复鼠标事件

			image {
				width: $all_width;
				height: $all_height;
			}
		}
	}
	
	/deep/ .u-model__title[data-v-3626fcec] {
		color: #fff !important;
		padding: 10px !important;
		background: #3e97b0 !important;
		font-weight: bold !important;
	}
</style>