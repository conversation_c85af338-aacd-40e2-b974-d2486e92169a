<template>
	<view class="safe-area-header" :style="headerStyle">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="statusBarStyle"></view>
		
		<!-- 导航栏内容 -->
		<view class="nav-bar" :style="navBarStyle">
			<!-- 左侧按钮 -->
			<view class="nav-left" @click="onLeftClick">
				<u-icon 
					v-if="showBack" 
					name="arrow-left" 
					:size="leftIconSize" 
					:color="leftIconColor"
				></u-icon>
				<text v-if="leftText" class="left-text" :style="leftTextStyle">{{ leftText }}</text>
			</view>
			
			<!-- 中间标题 -->
			<view class="nav-center">
				<text class="nav-title" :style="titleStyle">{{ title }}</text>
			</view>
			
			<!-- 右侧按钮 -->
			<view class="nav-right" @click="onRightClick">
				<u-icon 
					v-if="rightIcon" 
					:name="rightIcon" 
					:size="rightIconSize" 
					:color="rightIconColor"
				></u-icon>
				<text v-if="rightText" class="right-text" :style="rightTextStyle">{{ rightText }}</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'SafeAreaHeader',
		props: {
			// 标题
			title: {
				type: String,
				default: '标题'
			},
			// 背景颜色
			bgColor: {
				type: String,
				default: '#1296db'
			},
			// 标题颜色
			titleColor: {
				type: String,
				default: '#ffffff'
			},
			// 是否显示返回按钮
			showBack: {
				type: Boolean,
				default: false
			},
			// 左侧文字
			leftText: {
				type: String,
				default: ''
			},
			// 左侧图标颜色
			leftIconColor: {
				type: String,
				default: '#ffffff'
			},
			// 左侧图标大小
			leftIconSize: {
				type: [String, Number],
				default: '44'
			},
			// 右侧图标
			rightIcon: {
				type: String,
				default: ''
			},
			// 右侧文字
			rightText: {
				type: String,
				default: ''
			},
			// 右侧图标颜色
			rightIconColor: {
				type: String,
				default: '#ffffff'
			},
			// 右侧图标大小
			rightIconSize: {
				type: [String, Number],
				default: '44'
			},
			// 导航栏高度
			navHeight: {
				type: [String, Number],
				default: '44'
			}
		},
		data() {
			return {
				statusBarHeight: 0,
				isIPhoneX: false
			};
		},
		computed: {
			headerStyle() {
				return {
					backgroundColor: this.bgColor
				};
			},
			statusBarStyle() {
				return {
					height: this.statusBarHeight + 'px'
				};
			},
			navBarStyle() {
				return {
					height: this.navHeight + 'px',
					backgroundColor: this.bgColor
				};
			},
			titleStyle() {
				return {
					color: this.titleColor,
					fontSize: '36rpx',
					fontWeight: 'bold'
				};
			},
			leftTextStyle() {
				return {
					color: this.leftIconColor,
					fontSize: '32rpx'
				};
			},
			rightTextStyle() {
				return {
					color: this.rightIconColor,
					fontSize: '32rpx'
				};
			}
		},
		mounted() {
			this.getSystemInfo();
		},
		methods: {
			/**
			 * 获取系统信息
			 */
			getSystemInfo() {
				try {
					const systemInfo = uni.getSystemInfoSync();
					console.log('系统信息:', systemInfo);
					
					// 获取状态栏高度
					this.statusBarHeight = systemInfo.statusBarHeight || 0;
					
					// 判断是否为 iPhone X 系列
					this.isIPhoneX = this.checkIsIPhoneX(systemInfo);
					
					console.log('状态栏高度:', this.statusBarHeight);
					console.log('是否为 iPhone X 系列:', this.isIPhoneX);
				} catch (error) {
					console.error('获取系统信息失败:', error);
					// 设置默认值
					this.statusBarHeight = 20;
				}
			},
			
			/**
			 * 检查是否为 iPhone X 系列
			 */
			checkIsIPhoneX(systemInfo) {
				const { model, platform } = systemInfo;
				
				if (platform !== 'ios') return false;
				
				// iPhone X 系列的特征
				const iPhoneXModels = [
					'iPhone X',
					'iPhone XS',
					'iPhone XS Max',
					'iPhone XR',
					'iPhone 11',
					'iPhone 11 Pro',
					'iPhone 11 Pro Max',
					'iPhone 12',
					'iPhone 12 mini',
					'iPhone 12 Pro',
					'iPhone 12 Pro Max',
					'iPhone 13',
					'iPhone 13 mini',
					'iPhone 13 Pro',
					'iPhone 13 Pro Max',
					'iPhone 14',
					'iPhone 14 Plus',
					'iPhone 14 Pro',
					'iPhone 14 Pro Max'
				];
				
				return iPhoneXModels.some(iPhoneModel => 
					model.includes(iPhoneModel)
				);
			},
			
			/**
			 * 左侧按钮点击事件
			 */
			onLeftClick() {
				if (this.showBack) {
					// 默认返回行为
					uni.navigateBack({
						delta: 1,
						fail: () => {
							// 如果返回失败，跳转到首页
							uni.reLaunch({
								url: '/pages/tabbar/index'
							});
						}
					});
				}
				
				this.$emit('leftClick');
			},
			
			/**
			 * 右侧按钮点击事件
			 */
			onRightClick() {
				this.$emit('rightClick');
			}
		}
	};
</script>

<style scoped lang="scss">
	.safe-area-header {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 999;
		width: 100%;
	}
	
	.status-bar {
		width: 100%;
	}
	
	.nav-bar {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 30rpx;
		position: relative;
	}
	
	.nav-left,
	.nav-right {
		display: flex;
		align-items: center;
		min-width: 120rpx;
		height: 100%;
	}
	
	.nav-left {
		justify-content: flex-start;
	}
	
	.nav-right {
		justify-content: flex-end;
	}
	
	.nav-center {
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.nav-title {
		text-align: center;
		max-width: 400rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
	
	.left-text,
	.right-text {
		margin-left: 10rpx;
	}
</style>
