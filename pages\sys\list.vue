<template>
	<view class="wrap ">
		<!-- <js-lang title="home.title" :showBtn="true"></js-lang> -->
		<!-- <u-swiper :height="300" :list="imgList" :title="false" @click="imgListClick"></u-swiper> -->
		<view v-for="res in menuList2" :key="res.menuCode">
			<view class="cu-bar bg-white solid-bottom">
				<view class="action ">
					<u-icon name="/static/image/zfgs/index/icon@2x(6).png" size="80"></u-icon>
					<text class="text-lg  text-bold " style="font-size: 42rpx;">{{res.menuName}}</text>
				</view>
			</view>
			<view class="flex margin-sm flex-wrap justify-between u-skeleton">
				<view class="flex bg-white padding radius " v-for="item in res.childList"
					:key="item.menuName" @click="navTo(item)" style="margin-bottom: 15rpx;">
					<view class="xm-item u-skeleton-rect">
						<view class="xm-item-1">{{item.menuName}}</view>
						<view class="xm-item-2">{{item.menuTitle}}</view>
					</view>
					<image style="width: 86rpx; height: 86rpx" :src="item.menuIcon" mode="aspectFit"
						class="u-skeleton-circle" />
				</view>
			</view>
			
			<!-- <view class="margin-sm ">
				<view class="flex bg-white padding radius justify-between" v-for="item in res.childList"
					:key="item.menuName" @click="navTo(item.url)" style="margin-bottom: 20rpx;">
					<view class="xm-item u-skeleton-rect">
						<view class="xm-item-1">{{item.menuName}}</view>
						<view class="xm-item-2">{{item.menuTitle}}</view>
					</view>
					<image style="width: 86rpx; height: 86rpx" :src="item.menuIcon" mode="aspectFit"
						class="u-skeleton-circle" />
				</view>
			</view> -->
		</view>
		<view style="height: 1px"></view>
		<view style="height: 108rpx"></view>
		<!-- <custom-tab-bar ref="customtab1" :current-index="currentIndex"  @changeTab="handleTabChange" v-if="customTabBar" /> -->

	</view>
</template>

<script>
	import {
		mapState
	} from 'vuex';
	import checkVersion from "@/pages/lq-upgrade/checkVersion.js";
	import customTabBar from '@/pages/m8/componetns/customTabBar.vue';
	export default {
		components: {
			customTabBar
		},
		data() {
			return {
				currentIndex:1,
				customTabBar:true,
				model:{},
				selectList:[],
				stringPermissions: [],
				imgList: [
					{image: '/static/jeesite/banner/1.png'},
					{image: '/static/jeesite/banner/3.png'},
					{image: '/static/jeesite/banner/2.jpg'},
					{image: '/static/jeesite/banner/4.png'},
				],
				todoCount: 0,
				// menuList2 现在通过 computed 属性从 Vuex 获取
				menuList1:[],
				menuList: [
					{
						extend:{
							extendS2:'menue:ktnw:djh'
						},
						menuIcon: '/static/image/zfgs/index/jhth.png',
						url: '/pages/ktnw/jh/list?type=0',
					},
					{
						extend:{
							extendS2:'menue:ktnw:jhjl'
						},
						menuIcon: '/static/image/zfgs/index/ddlb.png',
						url: '/pages/ktnw/qtsj/list?busType=3,4',
					},
					{
						extend:{
							extendS2:'menue:ktnw:yjh'
						},
						menuIcon: '/static/image/zfgs/index/jhth.png',
						url: '/pages/ktnw/jh/list?type=1',
					},
					{
						extend:{
							extendS2:'menue:ktnw:ckdd'
						},
						// menuIcon: '/static/image/zfgs/index/jhjl.png',
						menuIcon: '/static/image/zfgs/index/ckdd.png',
						url: '/pages/ktnw/ckdd/list',
					},
					
					
					// 其它上架
					{
						extend:{
							extendS2:'menue:ktnw:qtsj'
						},
						menuIcon: '/static/image/zfgs/index/sj.png',
						url: '/pages/ktnw/qtsj/index',
					},
					{
						extend:{
							extendS2:'menue:ktnw:sjjl'
						},
						menuIcon: '/static/image/zfgs/index/ddlb.png',
						url: '/pages/ktnw/qtsj/list?busType=1',
					},
					// 其它下架
					{
						extend:{
							extendS2:'menue:ktnw:qtxj'
						},
						menuIcon: '/static/image/zfgs/index/xj.png',
						url: '/pages/ktnw/qtxj/index',
					},
					{
						extend:{
							extendS2:'menue:ktnw:xjjl'
						},
						menuIcon: '/static/image/zfgs/index/ddlb.png',
						url: '/pages/ktnw/qtsj/list?busType=2',
					},
					// 货位调整
					{
						extend:{
							extendS2:'menue:ktnw:hwtz'
						},
						menuIcon: '/static/image/zfgs/index/hwtz.png',
						url: '/pages/ktnw/hwtz/index',
					},
					// 销售退货
					{
						extend:{
							extendS2:'menue:ktnw:xsth'
						},
						menuIcon: '/static/image/zfgs/index/xsth.png',
						url: '/pages/ktnw/xsth/list',
					},
					// 入库上架
					{
						extend:{
							extendS2:'menue:ktnw:rksj'
						},
						menuIcon: '/static/image/zfgs/index/sj.png',
						url: '/pages/ktnw/rksj/list2',
					},
					// 商品重量
					{
						extend:{
							extendS2:'menue:ktnw:spzl'
						},
						menuIcon: '/static/image/zfgs/index/ckdd.png',
						url: '/pages/ktnw/spzl/index',
					},
				
				],
			}
		},
		computed: {
			...mapState(['vuex_company', 'vuex_warehouseMenuList']),

			// 过滤出 isShow 为 true 的仓库菜单
			menuList2() {
				if (!this.vuex_warehouseMenuList || !Array.isArray(this.vuex_warehouseMenuList)) {
					console.warn('仓库菜单数据无效，返回空数组');
					return [];
				}

				// 过滤分类和子菜单项，只显示 isShow 为 true 的
				const filteredMenus = this.vuex_warehouseMenuList.map(category => {
					// 如果分类本身 isShow 为 true，显示分类
					if (category.isShow === true) {
						// 过滤子菜单项，只显示 isShow 为 true 的
						const visibleChildren = category.childList ?
							category.childList.filter(child => child.isShow === true) : [];

						return {
							...category,
							childList: visibleChildren
						};
					}

					// 如果分类 isShow 为 false，但有可见的子菜单项，也显示分类
					if (category.childList) {
						const visibleChildren = category.childList.filter(child => child.isShow === true);
						if (visibleChildren.length > 0) {
							return {
								...category,
								childList: visibleChildren
							};
						}
					}

					return null; // 过滤掉
				}).filter(category => category !== null);

				console.log('仓库菜单过滤结果:', {
					原始分类数: this.vuex_warehouseMenuList.length,
					过滤后分类数: filteredMenus.length,
					可见菜单项总数: filteredMenus.reduce((total, category) => total + (category.childList?.length || 0), 0)
				});

				return filteredMenus;
			}
		},
		onShow() {
			// this.$u.api.ktnw.companyTreeData().then(res => {
			// 	this.selectList = res;
			// });
			
			// this.$u.api.ktnw.getCorpCache().then(res => {
			// 	this.model = res.data
			// 	this.$u.vuex('vuex_company', this.model);
			// });
			
			this.customTabBar = false ;
			setTimeout(()=>{
			    this.customTabBar = true ;
			},100)
		},
		created() {
			 
		},
		onLoad() {
			this.upgrade();
		},
		methods: {
			handleTabChange(pagePath) {
			  // 切换页面逻辑，可使用uni.navigateTo等API
			  uni.reLaunch({ url: pagePath });
			},
			//检查版本更新
			upgrade() {
			  // #ifdef APP-PLUS
			  this.$u.api.upgradeCheck().then((res) => {
			    let url = res.data.apkUrl
			      ? res.data.apkUrl
			      : this.vuex_config.xtUrl + res.data.xtUrl;
			    if (res.result == "true") {
			      checkVersion({
			        name: res.data.upTitle, //最新版本名称
			        code: res.data.upVersion, //最新版本号
			        content: `${res.data.upContent}`, //更新内容
			        url, //下载链接
			        // forceUpdate: true, //是否强制升级
			        forceUpdate: res.data.upType == '3'?true:false, //是否强制升级
			      });
			    }
			  });
			  // #endif
			},
			//显示菜单
			showMenu(list) {
				this.menuList1 = list
				this.menuList.forEach(item => {
					this.menuList1.forEach(res => {
						res.childList.forEach(req => {
							if (req.extend?.extendS2 == item.extend?.extendS2 ) {
								req.menuIcon = item.menuIcon;
								req.url = item.url;
							}
						})

					})
				})
			},
			async selectConfirm() {
				await this.$u.api.ktnw.switchCorp({
						companyCode: this.model.companyCode
					})
					.then(() => {
						this.$u.vuex('vuex_company', this.model);
					});
			},
			navTo(item) {
				console.log('navTo item:', item);

				// 如果传入的是字符串，保持向后兼容
				if (typeof item === 'string') {
					uni.navigateTo({
						url: item
					});
					return;
				}

				// 如果传入的是对象，使用 url 属性
				if (item && item.url) {
					uni.navigateTo({
						url: item.url
					});
				} else {
					console.warn('navTo: 无效的导航参数', item);
				}
			},
			imgListClick(index) {
				console.log(`点击了第${index + 1}页图片`)
			},
			itemClick(index) {
				console.log(index);
			}
		}
	}
</script>

<style scoped lang="scss">
	.cu-item {
		width: 41% !important;
		height: 90px;
	}

	.cuIcon-cardboardfill {
		margin-top: 20px !important;
	}

	.xm-title {
		border-radius: 20rpx;
		font-size: 36rpx;
		background-color: #fff;
		// background-image: url("/static/image/zfgs/index/<EMAIL>");
		// background-repeat: no-repeat;
		// background-size: 38rpx 42rpx;
		// background-position: 10rpx 10rpx;
		position: relative;

		.xm-title-p1 {
			color: #999999;
			font-size: 36rpx;
			line-height: 40rpx;
			margin-bottom: 8rpx;
		}

		.xm-title-p2 {
			color: #3d3d3d;
			line-height: 48rpx;
		}

		.xm-title-img {
			width: 38rpx;
			height: 42rpx;
			position: absolute;
			top: 0;
			right: 32rpx;
		}
	}

	.xm-item-1 {
		font-size: 42rpx;
		color: #3d3d3d;
		line-height: 40rpx;
		position: relative;
		margin-bottom: 26rpx;
	}

	.xm-item-1::after {
		content: "";
		display: inline-block;
		position: absolute;
		width: 32rpx;
		height: 4rpx;
		background-color: #bbbbbb;
		bottom: -16rpx;
		left: 0;
	}

	.xm-item {
		width: 200rpx;
	}

	.radius {
		border-radius: 20rpx;
	}

	.xm-item-2 {
		font-size: 34rpx;
		color: #999999;
	}
</style>