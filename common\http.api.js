/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 */
// 此处第二个参数vm，就是我们在页面使用的this，你可以通过vm获取vuex等操作
const install = (Vue, vm) => {



	// 参数配置对象
	const config = vm.vuex_config;

	// 将各个定义的接口名称，统一放进对象挂载到vm.$u.api(因为vm就是this，也即this.$u.api)下
	vm.$u.api = {

		// 基础服务：登录登出、身份信息、菜单授权、切换系统、字典数据等
		lang: (params = {}) => vm.$u.get('/lang/' + params.lang),
		index: (params = {}) => vm.$u.get(config.adminPath + '/index', params),
		login: (params = {}) => vm.$u.post(config.adminPath + '/login', params),
		logout: (params = {}) => vm.$u.get(config.adminPath + '/logout', params),
		authInfo: (params = {}) => vm.$u.get(config.adminPath + '/authInfo', params),
		menuTree: (params = {}) => vm.$u.get(config.adminPath + '/menuTree', params),
		switchSys: (params = {}) => vm.$u.get(config.adminPath + '/switch/' + params.sysCode),
		dictData: (params = {}) => vm.$u.get(config.adminPath + '/sys/dictData/treeData', params),
		dictListData: (params = {}) => vm.$u.get(config.adminPath + '/sys/dictData/listData', params),
		// 账号服务：验证码接口、忘记密码接口、注册账号接口等
		validCode: (params = {}) => vm.$u.getText('/validCode', params),
		getFpValidCode: (params = {}) => vm.$u.post('/account/getFpValidCode', params),
		savePwdByValidCode: (params = {}) => vm.$u.post('/account/savePwdByValidCode', params),
		getRegValidCode: (params = {}) => vm.$u.post('/account/getRegValidCode', params),
		saveRegByValidCode: (params = {}) => vm.$u.post('/account/saveRegByValidCode', params),
		
		// 解绑
		cleanWxOpenid: (params = {}) => vm.$u.post('/weixinMa/cleanWxOpenid', params),
		// 获取openId
		getOpenId: (params = {}) => vm.$u.post('/weixinMa/getOpenId', params),


		// APP公共服务
		upgradeCheck: () => vm.$u.post('/app/upgrade/check', { appCode: config.appCode, appVersion: config.appVersion }),
		commentSave: (params = {}) => vm.$u.post('/app/comment/save', params),

		// 个人信息修改
		user: {
			infoSaveBase: (params = {}) => vm.$u.post(config.adminPath + '/sys/user/infoSaveBase', params),
			infoSavePwd: (params = {}) => vm.$u.post(config.adminPath + '/sys/user/infoSavePwd', params),
			infoSavePqa: (params = {}) => vm.$u.post(config.adminPath + '/sys/user/infoSavePqa', params),
		},

		// 员工用户查询
		empUser: {
			listData: (params = {}) => vm.$u.get(config.adminPath + '/sys/empUser/listData', params),
		},

		// 组织机构查询
		office: {
			treeData: (params = {}) => vm.$u.get(config.adminPath + '/sys/office/treeData', params),
		
		},

		// 增删改查例子
		testData: {
			form: (params = {}) => vm.$u.post(config.adminPath + '/test/testData/form', params),
			list: (params = {}) => vm.$u.post(config.adminPath + '/test/testData/listData', params),
			save: (params = {}) => vm.$u.postJson(config.adminPath + '/test/testData/save', params),
			disable: (params = {}) => vm.$u.post(config.adminPath + '/test/testData/disable', params),
			enable: (params = {}) => vm.$u.post(config.adminPath + '/test/testData/enable', params),
			delete: (params = {}) => vm.$u.post(config.adminPath + '/test/testData/delete', params),
		},

		
		// 工作流引擎
		bpm: {
			myRuntimeList: (params = {}) => vm.$u.post(config.adminPath + '/bpm/bpmMyRuntime/listData', params),
			myRuntimeForm: (params = {}) => vm.$u.post(config.adminPath + '/bpm/bpmMyRuntime/form', params),
			myTaskList: (params = {}) => vm.$u.post(config.adminPath + '/bpm/bpmMyTask/listData', params),
			myTaskForm: (params = {}) => vm.$u.post(config.adminPath + '/bpm/bpmMyTask/form', params),
			getProcIns: (params = {}) => vm.$u.post(config.adminPath + '/bpm/bpmRuntime/getProcIns', params),
			getTask: (params = {}) => vm.$u.post(config.adminPath + '/bpm/bpmTask/getTask', params),
			stop: (params = {}) => vm.$u.post(config.adminPath + '/bpm/bpmRuntime/stop', params),
			stopProcess: (params = {}) => vm.$u.post(config.adminPath + '/bpm/bpmRuntime/stopProcess', params),
			claim: (params = {}) => vm.$u.post(config.adminPath + '/bpm/bpmTask/claim', params),
			complete: (params = {}) => vm.$u.post(config.adminPath + '/bpm/bpmTask/complete', params),
			turn: (params = {}) => vm.$u.post(config.adminPath + '/bpm/bpmTask/turn', params),
			turnTask: (params = {}) => vm.$u.post(config.adminPath + '/bpm/bpmTask/turnTask', params),
			back: (params = {}) => vm.$u.post(config.adminPath + '/bpm/bpmTaskDef/back', params),	
			backTask: (params = {}) => vm.$u.post(config.adminPath + '/bpm/bpmTaskDef/backTask', params),
			bpmTaskDeForm: (params = {}) => vm.$u.post(config.adminPath + '/bpm/bpmTaskDef/form', params),
			move: (params = {}) => vm.$u.post(config.adminPath + '/bpm/bpmTask/move', params),
			moveTask: (params = {}) => vm.$u.post(config.adminPath + '/bpm/bpmTask/moveTask', params),
			rollback: (params = {}) => vm.$u.post(config.adminPath + '/bpm/bpmTask/rollback', params),
			// /bpm/bpmMyTask/typeData
			typeData: (params = {}) => vm.$u.post(config.adminPath + '/bpm/bpmMyTask/typeData', params),
			// /bpm/bpmMyTask/countData
			countData: (params = {}) => vm.$u.post(config.adminPath + '/bpm/bpmMyTask/countData', params),
		},
		//运输单位
		carVen:{
			treeData:(params = {}) => vm.$u.post(config.adminPath+'/mf/carven/carVen/treeData', params),
			treeDataApp:(params = {}) => vm.$u.post(config.adminPath+'/mf/carven/carVen/treeDataApp', params),
			listData:(params = {}) => vm.$u.post(config.adminPath+'/mf/carven/carVen/listData', params),
		},
		m8:{
			officeTreeData:(params = {}) => vm.$u.post(config.adminPath+'/sys/office/treeData', params),
			empUserTreeData:(params = {}) => vm.$u.post(config.adminPath+'/sys/empUser/treeData', params),
			machineListData:(params = {}) => vm.$u.post(config.adminPath+'/m8/machine/listData', params),
			machineForm:(params = {}) => vm.$u.post(config.adminPath+'/m8/machine/form', params),
			checkMachineBar:(params = {}) => vm.$u.post(config.adminPath+'/m8/barcode/checkMachineBar', params),
			// checkFlowBar
			checkFlowBar:(params = {}) => vm.$u.post(config.adminPath+'/m8/barcode/checkFlowBar', params),
			posUpSave:(params = {}) => vm.$u.postJson(config.adminPath+'/wms/wh/app/posUpSave', params),
			posDownSave:(params = {}) => vm.$u.postJson(config.adminPath+'/wms/wh/app/posDownSave', params),
			posJustSave:(params = {}) => vm.$u.postJson(config.adminPath+'/wms/wh/app/posJustSave', params),
			getBarInfo:(params = {}) => vm.$u.post(config.adminPath+'/m8/barcode/getBarInfo', params),
			basInvListData:(params = {}) => vm.$u.post(config.adminPath+'/bas/inv/basInv/listData', params),
			whPosInvStockListData:(params = {}) => vm.$u.post(config.adminPath+'/wms/wh/pos/whPosInvStock/listData', params),
			whPosInvStockListSumData:(params = {}) => vm.$u.post(config.adminPath+'/wms/wh/pos/whPosInvStock/listSumData', params),
			
			debugSave:(params = {}) => vm.$u.post(config.adminPath+'/m8/app/api/debugSave', params),
			notifyFirstCheck:(params = {}) => vm.$u.post(config.adminPath+'/m8/app/api/NotifyFirstCheck', params),
			endWork:(params = {}) => vm.$u.post(config.adminPath+'/m8/app/api/endWork', params),
			beginWork:(params = {}) => vm.$u.post(config.adminPath+'/m8/app/api/beginWork', params),
			comfirmShangJi:(params = {}) => vm.$u.post(config.adminPath+'/m8/app/api/comfirmShangJi', params),
			comfirmXiaJi:(params = {}) => vm.$u.post(config.adminPath+'/m8/app/api/comfirmXiaJi', params),
			comfirmErCiJiaGongShangJi:(params = {}) => vm.$u.post(config.adminPath+'/m8/app/api/comfirmErCiJiaGongShangJi', params),
			comfirmFanXiuShangJi:(params = {}) => vm.$u.post(config.adminPath+'/m8/app/api/comfirmFanXiuShangJi', params),
			
			
			taskLastNodeData:(params = {}) => vm.$u.post(config.adminPath+'/m8/machine/task/taskLastNodeData', params),
			// /bas/pos/basPosition/listData
			posListData:(params = {}) => vm.$u.post(config.adminPath+'/wms/wh/pos/whPosCard/listData', params),
			// 
			repCheck:(params = {}) => vm.$u.postJson(config.adminPath+'/m8/app/api/repCheck', params),

			// /sys/app/findMenuList
			findMenuList:(params = {}) => vm.$u.post(config.adminPath+'/sys/app/findMenuList', params),


			// beginWork
			beginWorkFlow:(params = {}) => vm.$u.post(config.adminPath+'/m8/flow/app/beginWork', params),

			// endWork
			endWorkFlow:(params = {}) => vm.$u.post(config.adminPath+'/m8/flow/app/endWork', params),

			// repCheck
			repCheckFlow:(params = {}) => vm.$u.postJson(config.adminPath+'/m8/flow/app/repCheck', params),

			// /order/excuteLog/list
			excuteLogList:(params = {}) => vm.$u.post(config.adminPath+'/order/excuteLog/listData', params),

			// /order/m8FlowRepcheckLog/listData
			m8FlowRepcheckLogListData:(params = {}) => vm.$u.post(config.adminPath+'/order/m8FlowRepcheckLog/listData', params),

		},
		ktnw:{
			companyTreeData:(params = {}) => vm.$u.post(config.adminPath+'/sys/company/treeData', params),
			getCorpCache:(params = {}) => vm.$u.post(config.adminPath+'/common/getCorpCache', params),
			switchCorp:(params = {}) => vm.$u.post(config.adminPath+'/common/switchCorp', params),
			basInvListData:(params = {}) => vm.$u.post(config.adminPath+'/bas/inv/basInv/listData', params),
			basWarehouseTreeData:(params = {}) => vm.$u.get(config.adminPath+'/bas/house/basWarehouse/treeData', params),
			basPositionTreeData:(params = {}) => vm.$u.post(config.adminPath+'/bas/pos/basPosition/treeData', params),
			// 
			
			whPosInvStockListData:(params = {}) => vm.$u.post(config.adminPath+'/wms/wh/pos/whPosInvStock/listData', params),
			whPosInvStockListSumData:(params = {}) => vm.$u.post(config.adminPath+'/wms/wh/pos/whPosInvStock/listSumData', params),
			posJustSave:(params = {}) => vm.$u.postJson(config.adminPath+'/wms/wh/app/posJustSave', params),
			posUpSave:(params = {}) => vm.$u.postJson(config.adminPath+'/wms/wh/app/posUpSave', params),
			posDownSave:(params = {}) => vm.$u.postJson(config.adminPath+'/wms/wh/app/posDownSave', params),
			
			getBarInfo:(params = {}) => vm.$u.post(config.adminPath+'/barcode/decode/getBarInfo', params),
			// 
			listDataJh:(params = {}) => vm.$u.post(config.adminPath+'/wms/jh/app/listDataJh', params),
			// 
			listDataJhDetails:(params = {}) => vm.$u.post(config.adminPath+'/wms/jh/app/listDataJhDetails', params),
			// 
			listDataInvAndPos:(params = {}) => vm.$u.post(config.adminPath+'/wms/jh/app/listDataInvAndPos', params),
			// 
			jhConfirm:(params = {}) => vm.$u.postJson(config.adminPath+'/wms/jh/app/jhConfirm', params),
			// jhCallBack
			jhCallBack:(params = {}) => vm.$u.postJson(config.adminPath+'/wms/jh/app/jhCallBack', params),
			
			
			poJustListData:(params = {}) => vm.$u.post(config.adminPath+'/wms/wh/app/poJustListData', params),
			rdOrderListData:(params = {}) => vm.$u.post(config.adminPath+'/wms/rd/order/rdOrder/listData', params),
			rdOrderForm:(params = {}) => vm.$u.post(config.adminPath+'/wms/rd/order/rdOrder/form', params),
			rdOrderConfirm:(params = {}) => vm.$u.post(config.adminPath+'/wms/rd/order/rdOrder/confirm', params),
			rd02Form:(params = {}) => vm.$u.post(config.adminPath+'/wh/rd02/rd02/form', params),
			rd02Save:(params = {}) => vm.$u.postJson(config.adminPath+'/wh/rd02/rd02/save', params),
			findUpListData:(params = {}) => vm.$u.post(config.adminPath+'/wms/wh/app/findUpListData', params),
			xxthUpSave:(params = {}) => vm.$u.postJson(config.adminPath+'/wms/wh/app/xxthUpSave', params),
			
			rd10ListData:(params = {}) => vm.$u.post(config.adminPath+'/wms/rd/app/rd10ListData', params),
			rds10ListData:(params = {}) => vm.$u.post(config.adminPath+'/wms/rd/app/rds10ListData', params),
			rds10Form:(params = {}) => vm.$u.post(config.adminPath+'/wms/rd/app/form', params),
			posUpDelete:(params = {}) => vm.$u.post(config.adminPath+'/wms/rd/app/posUpDelete', params),
			rdposUpSave:(params = {}) => vm.$u.postJson(config.adminPath+'/wms/rd/app/posUpSave', params),
			rdconfirm:(params = {}) => vm.$u.postJson(config.adminPath+'/wms/rd/app/confirm', params),
			
			findGenList:(params = {}) => vm.$u.post(config.adminPath+'/wms/rd/app/findGenList', params),
			updateWeight:(params = {}) => vm.$u.post(config.adminPath+'/wms/common/app/updateWeight', params),
			jhPickMainListData:(params = {}) => vm.$u.post(config.adminPath+'/wms/jh/pick/jhPickMain/listData', params),
			
			
		},
		
		weixin:{
			// 企业微信登录
			wechatCallback: (params = {}) => vm.$u.post('/weixinCp/wechat/callback', params),
			// 小程序登录
			weixinMaCallback: (params = {}) => vm.$u.post('/weixinMa/wechat/callback', params),
		}

	};

}

export default {
	install
}