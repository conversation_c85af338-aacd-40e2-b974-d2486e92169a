<script>
	/**
	 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
	 */
	import { mapState } from 'vuex';

	export default {
		computed: {
			...mapState(['vuex_tabBarList', 'vuex_cartList'])
		},
		watch: {
			// 监听 TabBar 数据变化，触发动态 TabBar 更新
			vuex_tabBarList: {
				handler(newList) {
					// 无论数据是否为空都要触发更新，让DynamicUTabBar处理空数据的情况
					console.log('App.vue 检测到 TabBar 数据变化，触发动态 TabBar 更新:', newList);
					// 触发全局事件，通知动态 TabBar 组件更新
					uni.$emit('updateTabBar', newList || []);
				},
				immediate: false // 不立即执行，等待数据加载完成
			},
		},
		onLaunch() {
			// 国际化，设置当前语言
			if (this.vuex_locale) {
				this.$i18n.locale = this.vuex_locale;
				this.$u.api.lang({
					lang: this.vuex_locale
				});
			}
			// 设置底部导航栏角标
			// uni.setTabBarBadge({
			// 	index: 2,
			// 	text: this.vuex_cartList + ''
			// });
			// if (!this.vuex_cartList) {
			// 	uni.removeTabBarBadge({
			// 		index: 2
			// 	});
			// }
		}
	}
</script>
<style lang="scss">

	@import "uview-ui/index.scss";
	@import "pages/common/jeesite.scss";
	@import "colorui/main.css";
	@import "colorui/icon.css";
	@import "common/styles/scrollbar.scss";
</style>
<style lang="scss">
	uni-modal {
	  z-index: 999999 !important;
	}
</style>
