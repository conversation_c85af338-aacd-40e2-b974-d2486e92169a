{"tabMenuList": [{"text": "机床", "pagePath": "pages/m8/machine/list", "iconPath": "/static/image/zfgs/jc.png", "selectedIconPath": "/static/image/zfgs/jc_select.png", "extend": {"extendS2": "menu:m8:jichu<PERSON>"}, "isShow": false}, {"text": "车间", "pagePath": "pages/sys/jcList", "iconPath": "/static/image/zfgs/xm.png", "selectedIconPath": "/static/image/zfgs/xm_select.png", "extend": {"extendS2": "menu:m8:chejian"}, "isShow": false}, {"text": "仓库", "pagePath": "pages/sys/list", "iconPath": "/static/image/zfgs/ck.png", "selectedIconPath": "/static/image/zfgs/ck_select.png", "extend": {"extendS2": "menu:m8:cangku"}, "isShow": false}, {"text": "工序执行", "pagePath": "pages/sys/gxList", "iconPath": "/static/image/zfgs/daiban.png", "selectedIconPath": "/static/image/zfgs/daiban_select.png", "extend": {"extendS2": "menu:m8:gongxv<PERSON><PERSON>"}, "isShow": false}, {"text": "我的", "pagePath": "pages/sys/index/user/index", "iconPath": "/static/image/zfgs/my.png", "selectedIconPath": "/static/image/zfgs/my_select.png", "extend": {"extendS2": "menu:user:my"}, "isShow": true}], "processMenuList": [{"menuName": "业务", "isShow": true, "extend": {"extendS2": "menu:m8:gx:yewu"}, "childList": [{"menuName": "开工", "menuTitle": "工序执行", "extend": {"extendS2": "menu:m8:gx:kaigong"}, "menuIcon": "/static/image/ksjg.png", "isShow": false}, {"menuName": "完工", "menuTitle": "工序执行", "extend": {"extendS2": "menu:m8:gx:wangong"}, "menuIcon": "/static/image/jsjg.png", "isShow": false}, {"menuName": "送检", "menuTitle": "工序执行", "extend": {"extendS2": "menu:m8:gx:song<PERSON>an"}, "menuIcon": "/static/image/tzsj.png", "isShow": false}]}, {"menuName": "操作日志", "isShow": true, "extend": {"extendS2": "menu:m8:gx:log"}, "childList": [{"menuName": "处理中", "menuTitle": "工序执行", "extend": {"extendS2": "menu:m8:gx:chulizhong"}, "menuUrl": "/pages/m8/gongxu/list?status=1", "menuIcon": "/static/image/tsdj.png", "isShow": false}, {"menuName": "加工日志", "menuTitle": "工序执行", "extend": {"extendS2": "menu:m8:gx:ji<PERSON><PERSON><PERSON><PERSON>"}, "menuIcon": "/static/image/tsdj.png", "menuUrl": "/pages/m8/gongxu/list?status=2", "isShow": false}, {"menuName": "送检日志", "menuTitle": "工序执行", "extend": {"extendS2": "menu:m8:gx:<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "menuUrl": "/pages/m8/gongxu/repList", "menuIcon": "/static/image/tsdj.png", "isShow": false}]}], "WarehouseMenuList": [{"menuName": "业务", "isShow": true, "childList": [{"menuName": "存放", "menuTitle": "存放产品", "extend": {"extendS2": "meun.m8.ck.cunfang"}, "menuIcon": "/static/image/zfgs/index/xj.png", "url": "/pages/m8/ck/form?busType=1", "isShow": false}, {"menuName": "取出", "menuTitle": "取出产品", "extend": {"extendS2": "meun.m8.ck.quchu"}, "menuIcon": "/static/image/zfgs/index/sj.png", "url": "/pages/m8/ck/form?busType=2", "isShow": false}, {"menuName": "纠正", "menuTitle": "纠正数量", "extend": {"extendS2": "meun.m8.ck.jiuzheng"}, "menuIcon": "/static/image/zfgs/index/ckdd.png", "url": "/pages/m8/ck/jzslForm", "isShow": false}]}, {"menuName": "记录", "isShow": true, "childList": [{"menuName": "存取记录", "menuTitle": "存取记录", "extend": {"extendS2": "meun.m8.ck.cunqu<PERSON>lu"}, "menuIcon": "/static/image/zfgs/index/ddlb.png", "url": "/pages/m8/ck/list", "isShow": false}]}, {"menuName": "查询", "isShow": true, "childList": [{"menuName": "现存量", "menuTitle": "数据查询", "extend": {"extendS2": "meun.m8.ck.x<PERSON><PERSON><PERSON><PERSON>g"}, "menuIcon": "/static/image/ktnw/xcl.png", "url": "/pagesData/data/stockListData", "isShow": false}, {"menuName": "货位存量", "menuTitle": "数据查询", "extend": {"extendS2": "meun.m8.ck.huo<PERSON><PERSON>g"}, "menuIcon": "/static/image/ktnw/hwcl.png", "url": "/pagesData/data/posSumListData", "isShow": false}]}], "cwMenuList": [{"menuName": "业务", "isShow": true, "childList": [{"menuName": "送检", "menuTitle": "数铣送检", "menuIcon": "/static/image/tzsj.png", "url": "", "funcType": "rep<PERSON><PERSON><PERSON>", "extend": {"extendS2": "menu:m8:cj:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "isShow": false}]}], "machineMenuList": [{"extend": {"extendS2": "menu:m8:jc:t<PERSON><PERSON><PERSON><PERSON>"}, "menuIcon": "/static/image/sj.png", "menuName": "图纸上机", "menuTitle": "", "funcType": "s<PERSON><PERSON><PERSON>", "id": "1", "isShow": false}, {"extend": {"extendS2": "menu:m8:jc:t<PERSON><PERSON><PERSON><PERSON>"}, "menuIcon": "/static/image/xj.png", "menuName": "图纸下机", "menuTitle": "", "funcType": "xiaJi", "id": "2", "isShow": false}, {"extend": {"extendS2": "menu:m8:jc:ka<PERSON><PERSON><PERSON><PERSON>"}, "menuIcon": "/static/image/ksjg.png", "menuName": "开始加工", "menuTitle": "", "funcType": "beginWork", "id": "3", "isShow": false}, {"extend": {"extendS2": "menu:m8:jc:ji<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "menuIcon": "/static/image/jsjg.png", "menuName": "结束加工", "menuTitle": "", "funcType": "endWork", "id": "4", "isShow": false}, {"extend": {"extendS2": "menu:m8:jc:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "menuIcon": "/static/image/tzsj.png", "menuName": "通知首检", "menuTitle": "", "funcType": "notify<PERSON>irs<PERSON><PERSON><PERSON><PERSON>", "id": "5", "isShow": false}, {"extend": {"extendS2": "menu:m8:jc:t<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "menuIcon": "/static/image/tsdj.png", "menuName": "调试登记", "menuTitle": "", "funcType": "debug", "id": "6", "isShow": false}, {"extend": {"extendS2": "menu:m8:jc:er<PERSON><PERSON><PERSON><PERSON>"}, "menuIcon": "/static/image/ercijiagong.png", "menuName": "二次加工", "menuTitle": "", "funcType": "erCiJiaGong", "id": "7", "isShow": false}, {"extend": {"extendS2": "menu:m8:jc:ne<PERSON>ufan<PERSON>u"}, "menuIcon": "/static/image/fanxiu.png", "menuName": "内部返修", "menuTitle": "", "funcType": "neibuFanxiu", "id": "8", "isShow": false}]}