<template>
	<view class="wrap ">
		<!-- <js-lang title="home.title" :showBtn="true"></js-lang> -->
		<!-- <u-swiper :height="300" :list="imgList" :title="false" @click="imgListClick"></u-swiper> -->
		<view v-for="res in menuList2" :key="res.menuCode">
			<view class="cu-bar bg-white solid-bottom">
				<view class="action ">
					<u-icon name="/static/image/zfgs/index/icon@2x(6).png" size="80"></u-icon>
					<text class="text-lg  text-bold " style="font-size: 42rpx;">{{res.menuName}}</text>
				</view>
			</view>
			<view class="flex margin-sm flex-wrap justify-between u-skeleton">
				<view class="flex bg-white padding radius " v-for="item in res.childList"
					:key="item.menuName" @click="navTo(item.url)" style="margin-bottom: 15rpx;">
					<view class="xm-item u-skeleton-rect">
						<view class="xm-item-1">{{item.menuName}}</view>
						<view class="xm-item-2">{{item.menuTitle}}</view>
					</view>
					<image style="width: 86rpx; height: 86rpx" :src="item.menuIcon" mode="aspectFit"
						class="u-skeleton-circle" />
				</view>
			</view>

			<!-- <view class="margin-sm ">
				<view class="flex bg-white padding radius justify-between" v-for="item in res.childList"
					:key="item.menuName" @click="navTo(item.url)" style="margin-bottom: 20rpx;">
					<view class="xm-item u-skeleton-rect">
						<view class="xm-item-1">{{item.menuName}}</view>
						<view class="xm-item-2">{{item.menuTitle}}</view>
					</view>
					<image style="width: 86rpx; height: 86rpx" :src="item.menuIcon" mode="aspectFit"
						class="u-skeleton-circle" />
				</view>
			</view> -->
		</view>
		<view style="height: 1px"></view>
		<view style="height: 108rpx"></view>
		<!-- <custom-tab-bar ref="customtab1" :current-index="currentIndex"  @changeTab="handleTabChange" v-if="customTabBar" /> -->

	</view>
</template>

<script>
	import {
		mapState
	} from 'vuex';
	export default {
		name: 'WarehouseList',
		// components: {
		// 	customTabBar
		// },
		data() {
			return {
				currentIndex:1,
				customTabBar:true,
				model:{},
				selectList:[],
				stringPermissions: [],
				imgList: [
					{image: '/static/jeesite/banner/1.png'},
					{image: '/static/jeesite/banner/3.png'},
					{image: '/static/jeesite/banner/2.jpg'},
					{image: '/static/jeesite/banner/4.png'},
				],
				todoCount: 0,
				menuList1:[],
			}
		},
		computed: {
			...mapState(['vuex_company', 'vuex_warehouseMenuList']),

			// 过滤出 isShow 为 true 的仓库菜单
			menuList2() {
				if (!this.vuex_warehouseMenuList || !Array.isArray(this.vuex_warehouseMenuList)) {
					console.warn('仓库菜单数据无效，返回空数组');
					return [];
				}

				// 过滤分类和子菜单项，只显示 isShow 为 true 的
				const filteredMenus = this.vuex_warehouseMenuList.map(category => {
					// 如果分类本身 isShow 为 true，显示分类
					if (category.isShow === true) {
						// 过滤子菜单项，只显示 isShow 为 true 的
						const visibleChildren = category.childList ?
							category.childList.filter(child => child.isShow === true) : [];

						return {
							...category,
							childList: visibleChildren
						};
					}

					// 如果分类 isShow 为 false，但有可见的子菜单项，也显示分类
					if (category.childList) {
						const visibleChildren = category.childList.filter(child => child.isShow === true);
						if (visibleChildren.length > 0) {
							return {
								...category,
								childList: visibleChildren
							};
						}
					}

					return null; // 过滤掉
				}).filter(category => category !== null);

				console.log('仓库菜单过滤结果:', {
					原始分类数: this.vuex_warehouseMenuList.length,
					过滤后分类数: filteredMenus.length,
					可见菜单项总数: filteredMenus.reduce((total, category) => total + (category.childList?.length || 0), 0)
				});

				return filteredMenus;
			}
		},
		onShow() {
			console.log('仓库页面组件已挂载 - onShow');
			// this.$u.api.ktnw.companyTreeData().then(res => {
			// 	this.selectList = res;
			// });

			// this.$u.api.ktnw.getCorpCache().then(res => {
			// 	this.model = res.data
			// 	this.$u.vuex('vuex_company', this.model);
			// });

			this.customTabBar = false ;
			setTimeout(()=>{
			    this.customTabBar = true ;
			},100)
		},
		created() {

		},
		onLoad() {
			var _this = this;
		},
		mounted() {
			console.log('仓库页面组件已挂载 - mounted');
		},
		methods: {
			handleTabChange(pagePath) {
			  // 切换页面逻辑，可使用uni.navigateTo等API
			  uni.reLaunch({ url: pagePath });
			},

			//显示菜单
			showMenu(list) {
				this.menuList1 = list
				this.menuList.forEach(item => {
					this.menuList1.forEach(res => {
						res.childList.forEach(req => {
							if (req.extend?.extendS2 == item.extend?.extendS2 ) {
								req.menuIcon = item.menuIcon;
								req.url = item.url;
							}
						})

					})
				})
			},
			async selectConfirm() {
				await this.$u.api.ktnw.switchCorp({
						companyCode: this.model.companyCode
					})
					.then(res => {
						this.$u.vuex('vuex_company', this.model);
					});
			},
			navTo(url) {
				uni.navigateTo({
					url: url
				});
			},
			itemClick(index) {
				console.log(index);
			},
			imgListClick(index) {
				console.log(index);
			}
		}
	}
</script>

<style scoped lang="scss">
	.wrap {
		background-color: #f8f8f8;
		height: 100%;
		overflow-y: auto;
		overflow-x: hidden;
		/* 隐藏滚动条但保持滚动功能 */
		scrollbar-width: none; /* Firefox */
		-ms-overflow-style: none; /* IE 和 Edge */

		/* 当内容不足时不显示滚动条 */
		&::-webkit-scrollbar {
			display: none; /* Chrome, Safari, Opera */
		}
	}

	.cu-item {
		width: 41% !important;
		height: 90px;
	}

	.cuIcon-cardboardfill {
		margin-top: 20px !important;
	}

	.xm-title {
		border-radius: 20rpx;
		font-size: 36rpx;
		background-color: #fff;
		// background-image: url("/static/image/zfgs/index/<EMAIL>");
		// background-repeat: no-repeat;
		// background-size: 38rpx 42rpx;
		// background-position: 10rpx 10rpx;
		position: relative;

		.xm-title-p1 {
			color: #999999;
			font-size: 36rpx;
			line-height: 40rpx;
			margin-bottom: 8rpx;
		}

		.xm-title-p2 {
			color: #3d3d3d;
			line-height: 48rpx;
		}

		.xm-title-img {
			width: 38rpx;
			height: 42rpx;
			position: absolute;
			top: 0;
			right: 32rpx;
		}
	}

	.xm-item-1 {
		font-size: 42rpx;
		color: #3d3d3d;
		line-height: 40rpx;
		position: relative;
		margin-bottom: 26rpx;
	}

	.xm-item-1::after {
		content: "";
		display: inline-block;
		position: absolute;
		width: 32rpx;
		height: 4rpx;
		background-color: #bbbbbb;
		bottom: -16rpx;
		left: 0;
	}

	.xm-item {
		width: 200rpx;
	}

	.radius {
		border-radius: 20rpx;
	}

	.xm-item-2 {
		font-size: 34rpx;
		color: #999999;
	}
</style>