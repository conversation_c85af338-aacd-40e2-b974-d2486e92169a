<template>
	<view class="wrap">
		<!-- v-if="model.podid" -->
		<view >
			<js-error mode="bottom" ref="jsError"></js-error>
			<!-- <view class="cu-form-group">
				<view class="title">用户：</view>
				<view style="flex: 1;"> {{ model.soCode|| ""  }} </view>
			</view> -->
			<u-form class="form" :model="model" ref="uForm" label-position="left">
			<view class="padding-top padding-bottom">
				<view class="cu-form-group ">
					<view class="title">报检人：</view>
					<view style="flex: 1;"> {{ model.user&&model.user.userName ? model.user.userName : ''  }}</view>
				</view>
				<view class="cu-form-group " style="position: relative;">
					<view class="title ">报检时间：</view>
					<view style="flex: 1;">{{ model.repDate|| ""  }}</view>
				</view>
			</view>
			<view class="action bg-white"
				style="font-size: 16px;align-items: center;display: flex;padding: 10px 10px 10px 10px;">
				<u-icon name="/static/image/detail.png" size="60"></u-icon>
				<text class="cuIcon-titles text-bold">报检明细</text>		
			</view>
			<!-- style="padding: 10px;" -->
			<view >
				<view v-for="(item,index) in model.checkList" :key="index" class="cu-item shadow " >
					<view class="cu-form-group"	style="display: flex;justify-content: space-between;">
						<view v-if="item.erroMsg">
							<u-icon name="close" class="margin-right-xl" color="red" size="50" />
						</view>
						<view v-else>
							<u-icon name="checkbox-mark" class="margin-right-xl" color="green" size="50" />
						</view>
						<view style="flex: 1;">
							{{ item.orderPic && item.orderPic.picname ?  item.orderPic.picname : ''}}
						</view>
						<view>
							<text class="cuIcon-deletefill text-xxl text-orange"
								@tap="delsoOutDetail(item,index)"></text>
						</view>
					</view>
  					<view class="cu-form-group text-red text-xl" v-if="item.erroMsg">
						<view class="title margin-right-xl">错误提示：</view>
						<view style="flex: 1;" >{{ item.erroMsg }}</view>
					</view> 
					<view style="flex: 1;" v-if="!item.erroMsg">
						<view class="cu-form-group">
							<view class="title margin-right-xl">工序：</view>
							<view style="flex: 1;">
								{{ item.operCode ? "("+item.operCode+")": '' }} {{ item.oper && item.oper.operName ? item.oper.operName : ''}}
							</view>
						</view>                    
						<view class="cu-form-group">
							<view class="title"><text class="cuIcon-favorfill text-xs text-green"></text>数量：</view>
							<view style="flex: 1;">
								<u-input placeholder="请输入" v-model="item.iqty" type="digit" maxlength="200"></u-input>
							</view>
						</view>
					</view>
				</view>
			</view>
			</u-form>
		<view>
			<movable-area class="movable-area">
				<movable-view class="movable-view" :x="x" :y="y" direction="all">
					<u-button size="mini" @click="toAdd" type="primary" :loading="isSubmitting" :disabled="isSubmitting"
						style="width: 90px; height: 70px; color: #fff; font-size: 20px">
						{{ isSubmitting ? '提交中...' : '确认' }}
					</u-button>
				</movable-view>
			</movable-area>
		</view>
		</view>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				model: { 
					
				},
				isSubmitting: false,
				lastClickTime: 0, // 记录上次点击时间，防止双击
                soOutList: [],
				x: 640, //x坐标
				y: 800, //y坐标

			}
		},
		onLoad(params) {
			let that = this

			// 通过事件通道接收repCheck参数
			const eventChannel = this.getOpenerEventChannel();
			if (eventChannel) {
				eventChannel.on('repCheck', function(data) {
					console.log('')
					that.model = data || {};
					that.model.repDate = that.model.repCheck.repDate
					if (!Array.isArray(that.model.repCheck.checkList)) {
						that.model.checkList = [];
					} else {
						that.model.checkList = that.model.repCheck.checkList;
					}
					that.$forceUpdate()
				})
			}
		},
		onShow() {
		},
		methods: {

			makeSound(name){
				let src = '/static/'+name+'.mp3';
				//实例化声音  
				const Audio = uni.createInnerAudioContext();
				Audio.autoplay = true;
				Audio.src = src; //音频地址  
				Audio.play(); //执行播放  
				Audio.onError((res) => {
				});
				Audio.onPause(function() {
					console.log('end');
					Audio.destroy();
				});
			},





			
			handleFocus() {
				var _that = this;
				_that.focus = false;
				setTimeout(() => {
					_that.focus = true;
				}, 500)
			},
			// 判断输入的值children的每一项的iqty的值的和不能大于model的syInQty的值，小数点保留4位

			delsoOutDetail(item,index){
				console.log('item',item)
				let that = this
				uni.showModal({
					title: '删除提示',
					content: '是否删除此信息？',
					confirmColor: '#F54E40',
					success: (res) => {
						if (!res.confirm) {
							return false;
						}
						that.model.checkList.splice(index, 1);		
						that.$forceUpdate()
					}
				})
			},
			toAdd() {
				const currentTime = Date.now();

				// 防止双击（500ms内的重复点击）
				if (currentTime - this.lastClickTime < 500) {
					console.log('点击过快，请稍后再试');
					return;
				}
				this.lastClickTime = currentTime;

				// 过滤掉this.model.checkList中的errorMsg
				let checkList = this.model.checkList.filter(item => !item.erroMsg)
				// checkList 里面的每一项的iqty 大于 0
				let checkList1 = checkList.filter(item => item.iqty > 0)
				if (checkList1.length !== checkList.length) {
					this.$refs.jsError.showError('', '图纸数量大于0', 'error');
					return;
				}
				if (this.isSubmitting) {
						return;
				} else {
					try {
						this.isSubmitting = true;
						this.model.checkList = checkList1;
						this.$u.api.m8.repCheckFlow(this.model).then(res => {
							if (res.result == 'true') {
								this.$u.toast(res.message);
								setTimeout(() => {
									uni.navigateBack({
										delta: 1
									})
								}, 500)
							} else {
								this.$refs.jsError.showError('', res.message, 'error');
							}
						
						});
					} catch (error) {
						this.$refs.jsError.showError('', error.message, 'error');
					} finally {
						this.isSubmitting = false;
					} 	
				}

			},
		}
	}
</script>
<style scoped lang="scss">
	$all_width: 96rpx;
	$all_height: 96rpx;
	.movable-area {
		z-index: 999;
		height: 97vh;
		width: 650rpx;
		// max-width: 1152rpx;
		// 固定在右边
		top: -10rpx;
		position: fixed;
		right: $all_width;
		// 固定在右下角
		pointer-events: none; //此处要加，鼠标事件可以渗透
	
		.movable-view {
			width: $all_width;
			height: $all_height;
			pointer-events: auto; //恢复鼠标事件
	
			image {
				width: $all_width;
				height: $all_height;
			}
		}
	}
	
	.footer {
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		background: #fff;z-index: 999;
		border-top: 1px solid #eee;
	}
	.text-xxls{
		font-size:60rpx;
	}
	.hide {
		display: none;
	}

	.cu-bar {
		min-height: 60px;
	}


	.foot {
		background: #fff;
	}

	.cu-item {
		flex-wrap: wrap;
		padding: 8px 15px !important;
	}

	.title {
		width: 250;
	}
	.title1{
		width: 150rpx;
	}

	.cu-modal-slot {
		margin: 42rpx 32rpx 20rpx 32rpx;
		height: 100vh;
		position: relative;
	}

	.cu-modal-header {
		font-size: 38rpx;
		color: #3d3d3d;
		line-height: 40rpx;
		text-align: left;
		font-weight: 500;
	}

	.button {
		font-size: 32rpx;
		color: #666666;
		line-height: 40rpx;
		padding: 12rpx 40rpx;
		margin-bottom: 20rpx;
		background: #e2e2e2;
		border-radius: 180rpx;
	}

	.button:hover {
		background: #3e97b0;
		color: #ffffff;
	}

	.cu-modal-search-title {
		margin: 32rpx 0rpx 20rpx 0rpx;
		text-align: left;
		font-size: 32rpx;
		color: #999999;
		line-height: 40rpx;
	}

	.cu-dialog {
		background: #ffffff;
	}

	.cu-modal-footer {
		padding: 32rpx 32rpx;
		width: 100%;
	}

	.cu-btn {
		font-size: 16px;
		height: 40px;
		width: 50%;
	}

	.bg-confirm {
		background: #3e97b0;
		color: #ffffff;
	}
	uni-modal {
	  z-index: 999999 !important;
	}
	.text-xxl{
		font-size: 72rpx !important;
	}
</style>