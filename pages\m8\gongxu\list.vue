<template>
	<view style="display: flex;height: 100vh;flex-direction: column;">
		<js-error mode="bottom" ref="jsError"></js-error>
		<xw-scan></xw-scan>
		<view style="background-color: #fff;margin-bottom: 10px;">
			<!-- <view class="search">
				<u-search @click="show = true" :show-action="false" placeholder="搜索" :disabled="true"></u-search>
			</view> -->
			<view class="padding-sm flex light " style="background-color: #eee;align-items: center;" >
				<view style="width: 100%">
					<u-search @click="show = true" :show-action="false" placeholder="搜索" :disabled="true"></u-search>
				</view>
				<view style="margin-left: 10px; display: flex; flex-direction: column">
					<u-icon @click="reset" name="reload" size="60"></u-icon>
				</view>
			</view>
			<!-- <view class="cu-bar search" style="padding: 10px">
				<u-search v-model="barCode" ref="uSearch" :focus="focus" placeholder="请先聚焦后扫描" :show-action="false"
					@search="confirm"></u-search>
				
				<view style="margin-left: 10px; display: flex; flex-direction: column">
					<u-icon @click="show=true" name="list-dot" size="50"></u-icon>
				</view>
			</view> -->
		</view>
		<view>
			<u-popup v-model="show" mode="right" length="90%">
				<view
					style="font-size: 18px;border-bottom: 1px solid #aaa;font-weight: bold;padding: 10px;display: flex;justify-content: space-between;">
					<!-- <u-icon name="/static/image/ss.png" size="65"></u-icon> -->
					<text class=" padding-left-sm" style="color: #3E97B0;">筛选</text>
					<text @click="show=false">关闭</text>
				</view>
				<!-- class="form"  -->
				<u-form class="form" style="padding: 0 10px;" :model="query" ref="uForm" label-position="left">
					<!-- <view class="padding-sm flex">
						
					  <view style="width: 100%"
					      ><u-search
					        placeholder="请扫描存货/货位"
					        v-model="barCode"
					        :show-action="false"
					        @search="confirm()"
					        searchIconSize="26"
							:focus="focus"
					        :inputStyle="inputStyle"
					      ></u-search
					    ></view>
					    <u-icon
					      name="scan"
					      size="60"
					      class="margin-sm-left flex-sub margin-sm-right"
						  @click="search"
					    ></u-icon>
					  </view> -->
					
					<u-form-item class="text-bold" label="图号:" prop="picno" label-width="220">
						<u-input placeholder="请输入" v-model="query['orderPic.picno']" type="text" maxlength="200"></u-input>
					</u-form-item>
					<u-form-item class="text-bold" label="图纸:" prop="picname" label-width="220">
						<u-input placeholder="请输入" v-model="query['orderPic.picname']" type="text" maxlength="200"></u-input>
					</u-form-item>
                    <u-form-item class="text-bold" label="工序:" prop="operName" label-width="220">
						<u-input placeholder="请输入" v-model="query['oper.operName']" type="text" maxlength="200"></u-input>
					</u-form-item>
                    <u-form-item class="text-bold" label="负责人:" prop="manName" label-width="220">
						<u-input placeholder="请输入" v-model="query['manName']" type="text" maxlength="200"></u-input>
					</u-form-item>
					<u-form-item class="text-bold" label="用户(扫):" prop="userBarCode" label-width="220">
						<u-input placeholder="请输入" v-model="query['userBarCode']" type="text" maxlength="200"></u-input>
					</u-form-item>
					<u-form-item class="text-bold" label="图纸(扫):" prop="picBarCode" label-width="220">
						<u-input placeholder="请输入" v-model="query['picBarCode']" type="text" maxlength="200"></u-input>
					</u-form-item>
					
				</u-form>
				<!-- <view class="footer">
				<u-button class="btn" type="primary" @click="submit">查询</u-button>
			</view> -->
				<view class=" flex cu-bar tabbar bg-white foot cu-modal-footer">
					<!-- round -->
					<button class="cu-btn  lines-red lg " @click="reset">重置</button>
					<button class="cu-btn  bg-confirm lg margin-left " @click="submit">查询</button>
				</view>
			</u-popup>
			<!-- <u-calendar v-model="startTime" mode="range" @change="startConfirm" max-date="9999"></u-calendar> -->
			<u-picker mode="time" v-model="startTime" @confirm="startConfirm"></u-picker>
			<u-picker mode="time" v-model="startTime2" @confirm="startConfirm2"></u-picker>
		</view>
		<next-table class="next-table" style="flex: 1;" :show-header="true" :columns="column" :stripe="true" :fit="false" :border="true"  @pageChange="pageChange"
			:data="datalist" :showPaging="true" :pageIndex="query.pageNo" :pageTotal="pageTotal"  sum-text="总" show-summary :summary-method="summaryMethod"></next-table>
	
		<view>
			<movable-area class="movable-area1">
				<movable-view class="movable-view" :x="x" :y="y" direction="all">
					<u-button size="mini" @click="search" type="success"
						style="width: 90px; height: 70px; color: #fff; ">
						<view style="font-size: 16px;display: flex; flex-direction: column;text-align: center;">
							<u-icon style="margin: auto;" name="/static/jeesite/sm1.png" size="80"></u-icon>
							<view>扫一扫</view>
						</view>
					</u-button>
		
				</movable-view>
			</movable-area>
		</view>
	</view>
</template>
<script>
	import util from '@/common/fire.js'
	export default {
			options: {
				styleIsolation: 'shared'
			},
			data () {
		        return {
					x: 650, //x坐标
					y: 650, //y坐标
					model:{},
					barCode:'',
					companySelectList:[],
					query: {
						pageNo: 1,
						pageSize: 7,
						status: '',
					},
					inputStyle:{
					  fontSize:'34rpx',
					  marginRight: '10rpx' ,
					},
					show: false,
					startTime: false,
					startTime2: false,
					formDate:'',
					showDate: false,
					focus:false,
		            // pageIndex: 1,
		            pageTotal: 0,
					selectList: [],
		            datalist: [],
		            checkNameList: [],
		            column: [
						// fixed:true,
						{ name: 'xuhao', label: '序号', width:50 ,align:'center',fixed:true,},
						{ name: 'orderPic.picname', label: '图名', width:100,fixed:true,},
						{ name: 'orderPic.picno', label: '图号', width:120 ,},
						{ name: 'oper.operName', label: '工序', width:60 , align:'center',},
						{ name: 'beginTime', label: '开始时间', width:150 , align:'center',},
						{ name: 'endTime', label: '结束时间', width:150,align:'center',},
                        { name: 'iqty', label: '完工数', width:70,align:'center',},
                        { name: 'manName', label: '负责人', width:80,align:'center',},
                        { name: 'logType', label: '类型', width:60,align:'center',},
						{ name: 'm8Order.ccode', label: '订单号', width:120 , },
                        
		            ]
		        }
		    },
			onReady() {
				
			},
			onLoad(p) {
				console.log('p',p)
				if(p.status){
					this.query.status = p.status
					// 根据 status 设置页面标题
					this.setPageTitle(p.status)
				}
				if(p.invName){
					this.query['orderPic.picname'] = p.invName
					this.query['basInv.viewCode'] = p.viewCode
					this.query['posCode'] = p.posCode || ''
					this.query['oper.operName'] = p.cwhname  || ''
				}
				this.getdatalist()
			},
			onHide() {
				// console.log('页面销毁（onUnload）广播监听事件：xwscan')
				// 销毁广播监听事件
				uni.$off('xwscan', this.BroadcastScanningToObtainData)
			},
			onUnload() {
				// console.log('页面销毁（onUnload）广播监听事件：xwscan')
				// 销毁广播监听事件
				uni.$off('xwscan', this.BroadcastScanningToObtainData)
			},
			onShow() {
				// console.log('页面开启（onLoad）广播监听事件：xwscan')
				// 开启广播监听事件
				uni.$on('xwscan', this.BroadcastScanningToObtainData)
			},
		    methods: {
				/**
				 * 根据状态设置页面标题
				 */
				setPageTitle(status) {
					let title = '';
					if (status == '1') {
						title = '处理中';
					} else if (status == '2') {
						title = '加工日志';
					}

					if (title) {
						uni.setNavigationBarTitle({
							title: title
						});
						console.log('页面标题已设置为:', title);
					}
				},

				handleFocus() {
					var _that = this;
					_that.focus = false;
					setTimeout(() => {
						_that.focus = true;
					}, 500)
				},
				/** 发生声音*/
				sendMp3(name) {
					console.log("=====testClick=====");
					let src = '/static/jeesite/' + name + '.mp3';
					//实例化声音  
					const Audio = uni.createInnerAudioContext();
					Audio.autoplay = true;
					Audio.src = src; //音频地址  
					Audio.play(); //执行播放  
					Audio.onError((res) => {
						console.log(res.errMsg);
						console.log(res.errCode);
					});
					Audio.onPause(function() {
						console.log('end');
						Audio.destroy();
					});
				},
				summaryMethod({columns, data}){
					let sums = [];
					columns.forEach((column, index) => {
					        if (index === 1) {
					            sums[index] = '合计';
					            return;
					        }
					        const values = data.map(item => Number(item[column.name]));
					        const precisions = [];
					        let notNumber = true;
							console.log(values,'values',column.name)
					        values.forEach(value => {
					            if (column.name == 'iqty') {
					                notNumber = false;
					                let decimal = ('' + value).split('.')[1];
					                precisions.push(decimal ? decimal.length : 0);
					            }
					        });
					        const precision = Math.max.apply(null, precisions);
					        if (!notNumber) {
					            sums[index] = values.reduce((prev, curr) => {
					                const value = Number(curr);
					                if (!isNaN(value)) {
					                    return parseFloat((prev + curr).toFixed(Math.min(precision, 20)));
					                } else {
					                    return prev;
					                }
					            }, 0);
					        } else {
					            sums[index] = '';
					        }
					});
					return sums
				},
				invCode(bar, companyCode) {
					let InventoryPrefix = this.vuex_config.InventoryPrefix;
					if (bar.indexOf(InventoryPrefix) != -1) {
						return bar
					} else {
						let code = `inv_${companyCode}_${bar}`
						return code
					}
				},
				BroadcastScanningToObtainData(res) {
					//获取扫描到的条形码
					let barcode = res.code
					
					//判断条形码长度是否大于3
					if(barcode.length > 3){
						//去除换行符
						let newString = barcode.replace('\n;', '');
						
						this.barCode = newString;
						this.confirm()
						//将换行符分割成数组
						// const allItems = newString.split('\n');
						// 	//遍历数组，将每一项添加到arr中
						// 	for(let i = 0;i<allItems.length;i++){
						// 		this.arr.push({
						// 			"content":allItems[i],
						// 			"remarks":this.remarks
						// 		})
						// 	}
					}
				},
				confirm() {
					let _that = this;
					_that.focus = false
					let bar = encodeURIComponent(this.barCode)
					let orderPrefix = _that.vuex_config.orderPrefix;
					let userPrefix = _that.vuex_config.userPrefix;
					if (bar.indexOf(orderPrefix) != -1) {
						this.$forceUpdate()
						this.query.pageNo = 1;
						this.query.picBarCode = bar;
						this.loadData();
					} else if (bar.indexOf(userPrefix) != -1) {
						this.$forceUpdate()
						this.query.pageNo = 1;
						this.query.userBarCode = bar
						this.loadData();
					} else {
						_that.sendMp3('bcz');
						_that.$refs.jsError.showError("", "请扫描正确的图纸或用户", "error");
						setTimeout(() => {
							_that.focus = true;
							this.barCode = ''
						}, 500)
					}
				
				},
				search() {
					var _that = this;
					_that.focus = false
					uni.scanCode({
						scanType: ["barCode", "qrCode"],
						// onlyFromCamera: true,
						success: function(res) {
							_that.barCode = res.result;
							_that.confirm()
						},
					});
				},
				makeSound(name){
					console.log("=====testClick=====");
					let src = '/static/'+name+'.mp3';
					//实例化声音  
					const Audio = uni.createInnerAudioContext();
					Audio.autoplay = true;
					Audio.src = src; //音频地址  
					Audio.play(); //执行播放  
					Audio.onError((res) => {
					});
					Audio.onPause(function() {
						console.log('end');
						Audio.destroy();
					});
				},
				startConfirm(e) {
					this.query.ddate_gte = e.year + "-" + e.month + "-" + e.day;
				},
				startConfirm2(e) {
					this.query.ddate_lte = e.year + "-" + e.month + "-" + e.day;
				},
				submit() {
					setTimeout(() => {
						this.query.pageNo = 1;
						this.loadData();
					}, 100);
				},
				reset() {
					this.query = {
						pageNo: 1,
						pageSize: 7,
						status: this.query.status
					};
					this.loadData();
				},
				loadData(){
					this.show = false;
					this.query.pageNo = 1
					
					this.getdatalist()
				},
		        getdatalist() {
					this.$u.api.m8.excuteLogList(this.query).then((res) => {
						if(!res.list.length){
							this.query.pageNo = 0
						}
						this.datalist = res.list.map((item,index)=>{
							item.xuhao = index+1;
							item['oper.operName'] = item.oper.operName
							item['orderPic.picname'] = item.orderPic?.picname
							item['orderPic.picno'] = item.orderPic?.picno
							item['m8Order.ccode'] = item.m8Order?.ccode
                            this.$u.api.dictData({dictType: 'm8_flow_log_type'}).then(res => {
                                let arr = res.filter(itemFilter=>{
                                    return itemFilter.value == item.logType
                                })
                                if(arr.length){
                                    item.logType = arr[0].name
                                }
                                
                            })
							
							return item
						})
						this.pageTotal = Math.ceil(res.count /this.query.pageSize); 
					});
		        },
		        pageChange(index) {
		            this.query.pageNo = index
		            this.getdatalist()
		        },
		    },
		    created() {
		        // this.getdatalist()
		    }
	}
</script>
<style lang="scss">
	
	$all_width: 96rpx;
	$all_height: 96rpx;
	.movable-area1 {
		z-index: 888;
		height: 97vh;
		width: 650rpx;
		position: fixed;
		right: $all_width;
		top: -($all_height + 70rpx);
		pointer-events: none; //此处要加，鼠标事件可以渗透
	
		.movable-view {
			width: $all_width;
			height: $all_height;
			pointer-events: auto; //恢复鼠标事件
	
			image {
				width: $all_width;
				height: $all_height;
			}
		}
	}
	.text-xxl{
		font-size:60rpx;
	}
	page {
		// background-color: #f8f8f8;
		background-color:#e6e6e6;
		height: 100vh;
		overflow: hidden;
		
	}
	.cu-bar {
		min-height: 60px;
	}
	
	.cu-btn {
		font-size: 16px;
		height: 40px;
	}
	
	.foot {
		background: #fff;
	}
	.flex-sub{
		margin-left: 10rpx;
	}
	
	.button {
	  font-size: 32rpx;
	  color: #666666;
	  line-height: 40rpx;
	  padding: 12rpx 40rpx;
	  margin-bottom: 20rpx;
	  margin-right: 10rpx;
	  background: #f7f7f7;
	  border-radius: 180rpx;
	}
	.button:hover {
	  background: #3e97b0;
	  color: #ffffff;
	}

	.uni-group {
		display: flex;
		align-items: center;
	}
	.lable-text {
		text-align: justify;
	}
	
	.u-form-item {
		font-size: 28rpx !important;
		padding: 2px !important;
	}
	.footer {
		position: fixed;
		left: 0;
		bottom: 20px;
		width: 100%;
	}
	.cu-modal-footer {
		padding: 32rpx 32rpx;
		width: 100%;
	
		.cu-btn {
			width: 50%;
		}
	}
	.bg-confirm {
		background: #3e97b0;
		color: #ffffff;
	}
	/deep/.table-empty.data-v-3eadd49d {
		// background: #fff;
	    // height: 63vh !important;
		height: 100% !important;;
		border:none !important;;
	}
	
	/deep/.next-table>.data-v-68ab1c7c{
		height:  calc(100% - 50px);
		background: #fff;
		// overflow-y: hidden;
	}
	
	/deep/.next-table-scroll{
		height: 100%;
		display: flex;
		flex-direction: column;
	}
	/deep/.next-table-fixed{
		flex: 1;
		// overflow: scroll;
	}
	/deep/table-empty{
		flex: 1;
	}
	.text-bold {
		font-size: 17px !important;
		padding:10px 0px !important;
	}
	/deep/.u-form-item{
		padding:10px 0px !important;
	}
</style>