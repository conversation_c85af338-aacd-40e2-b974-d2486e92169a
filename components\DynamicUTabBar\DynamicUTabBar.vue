<template>
	<u-tabbar
		v-if="showTabBar && tabBarList.length > 0"
		:list="tabBarList"
		:value="currentTabBarIndex"
		:activeColor="activeColor"
		:inactiveColor="inactiveColor"
		:bgColor="bgColor"
		:height="height"
		:borderTop="borderTop"
		:hideTabBar="hideNativeTabBar"
		:iconSize="iconSize"
		:fontSize="fontSize"
		@change="onTabBarChange"
	>
	</u-tabbar>
</template>

<script>
	import { mapState } from 'vuex';
	import menuMatcher from '@/utils/menuMatcher.js';

	export default {
		name: 'DynamicUTabBar',
		props: {
			// 是否显示 TabBar
			show: {
				type: Boolean,
				default: true
			},
			// 激活颜色
			activeColor: {
				type: String,
				default: '#1296db'
			},
			// 未激活颜色
			inactiveColor: {
				type: String,
				default: '#909399'
			},
			// 背景颜色
			bgColor: {
				type: String,
				default: '#ffffff'
			},
			// TabBar 高度
			height: {
				type: [String, Number],
				default: '60px'
			},
			// 是否显示顶部边框
			borderTop: {
				type: Boolean,
				default: true
			},
			// 是否隐藏原生 TabBar
			hideNativeTabBar: {
				type: Boolean,
				default: true
			},
			// 图标大小
			iconSize: {
				type: [String, Number],
				default: '30px'
			},
			// 字体大小
			fontSize: {
				type: [String, Number],
				default: '18px'
			},
			// 当前选中索引
			currentIndex: {
				type: Number,
				default: 0
			}
		},
		data() {
			return {
				tabBarList: [],
				showTabBar: false,
				// 从 menu.json 获取 TabBar 配置池
				defaultTabBarPool: menuMatcher.localMenuList || [],
				// 固定的"我的" TabBar 配置
				myTabBarConfig: {
					text: "我的",
					pagePath: "pages/sys/index/user/index",
					iconPath: "/static/image/zfgs/my.png",
					selectedIconPath: "/static/image/zfgs/my_select.png",
					extend: { extendS2: "menu:user:my" }
				}
			};
		},
		computed: {
			...mapState(['vuex_tabBarList']),

			/**
			 * 当前 TabBar 索引
			 */
			currentTabBarIndex() {
				// 确保索引在有效范围内
				const index = this.currentIndex || 0;
				const maxIndex = this.tabBarList.length - 1;
				const validIndex = Math.max(0, Math.min(index, maxIndex));

				console.log(`=== TabBar索引计算 ===`);
				console.log(`传入索引: ${index}`);
				console.log(`TabBar长度: ${this.tabBarList.length}`);
				console.log(`最大索引: ${maxIndex}`);
				console.log(`有效索引: ${validIndex}`);
				console.log(`=== 计算结束 ===`);

				return validIndex;
			}
		},
		watch: {
			// 监听外部传入的 currentIndex 变化
			currentIndex: {
				handler(newIndex, oldIndex) {
					console.log(`DynamicUTabBar currentIndex 变化: ${oldIndex} -> ${newIndex}`);
					console.log('当前 TabBar 索引:', this.currentTabBarIndex);
				},
				immediate: true
			},

			// 监听 Vuex 中的 TabBar 数据变化
			vuex_tabBarList: {
				async handler(newList) {
					console.log('DynamicUTabBar 监听到 vuex_tabBarList 变化:', newList);
					// 无论数据是否为空都要更新，让buildDynamicTabBarConfig处理空数据情况
					await this.updateTabBarFromVuex(newList || []);
				},
				immediate: true
			},

			// 监听显示属性
			show: {
				handler(newVal) {
					this.showTabBar = newVal && this.shouldShowTabBar();
				},
				immediate: true
			}
		},
		mounted() {
			this.initTabBar();
			this.listenPageChange();
		},
		methods: {
			/**
			 * 初始化 TabBar
			 */
			async initTabBar() {
				console.log('初始化 DynamicUTabBar');

				try {
					// 检查是否应该显示 TabBar
					if (!this.shouldShowTabBar()) {
						console.log('当前页面不应该显示 TabBar');
						this.showTabBar = false;
						return;
					}

					// 无论数据是否为空都要更新，让buildDynamicTabBarConfig处理空数据情况
					await this.updateTabBarFromVuex(this.vuex_tabBarList || []);
				} catch (error) {
					console.error('初始化 TabBar 失败:', error);
					// 使用最简单的配置
					this.initSimpleTabBar();
				}
			},
			
			/**
			 * 从 Vuex 更新 TabBar
			 */
			async updateTabBarFromVuex(vuexTabBarList) {
				console.log('从 Vuex 更新 DynamicUTabBar:', vuexTabBarList);

				try {
					// 使用内置方法处理数据
					const dynamicConfig = await this.buildDynamicTabBarConfig(vuexTabBarList);

					// 转换为 uView TabBar 格式
					this.tabBarList = this.convertToUViewFormat(dynamicConfig);

					// 更新当前索引
					this.updateCurrentIndex();

					this.showTabBar = this.shouldShowTabBar() && this.show;

					console.log('DynamicUTabBar 更新完成:', {
						list: this.tabBarList,
						currentIndex: this.internalCurrentIndex,
						show: this.showTabBar
					});
				} catch (error) {
					console.error('更新 TabBar 失败:', error);
					// 使用默认配置
					this.useDefaultTabBar();
				}
			},
			
			/**
			 * 转换为 uView TabBar 格式
			 */
			convertToUViewFormat(config) {
				// 检查 config 是否为数组
				if (!Array.isArray(config)) {
					console.error('convertToUViewFormat 参数错误，期望数组，实际收到:', typeof config, config);
					return [];
				}

				return config.map((item, index) => ({
					// uView TabBar 必需字段
					iconPath: item.iconPath,
					selectedIconPath: item.selectedIconPath,
					text: item.text,
					// 在容器模式下，不设置 pagePath，让 uView TabBar 使用 value 模式
					// pagePath: item.pagePath,

					// 扩展信息
					extend: item.extend,
					index: index,
					originalPagePath: item.pagePath  // 保存原始路径用于页面跳转
				}));
			},
			
			/**
			 * 使用默认 TabBar 配置
			 */
			async useDefaultTabBar() {
				console.log('使用默认 DynamicUTabBar 配置（从 menu.json 获取）');

				try {
					// 从 menu.json 中获取 isShow 为 true 的菜单项
					const originalMenus = menuMatcher.localMenuList || [];
					const defaultVisibleMenus = originalMenus.filter(item => item.isShow === true);

					console.log('从 menu.json 获取到的默认显示菜单:', defaultVisibleMenus.length, '项');
					defaultVisibleMenus.forEach(item => {
						console.log(`  - ${item.text} (${item.extend?.extendS2})`);
					});

					if (defaultVisibleMenus.length > 0) {
						// 转换为 API 格式
						const defaultMenuItems = defaultVisibleMenus.map(item => ({
							menuName: item.text,
							menuUrl: item.pagePath.startsWith('/') ? item.pagePath : `/${item.pagePath}`,
							extend: item.extend
						}));

						const dynamicConfig = await this.buildDynamicTabBarConfig(defaultMenuItems);
						this.tabBarList = this.convertToUViewFormat(dynamicConfig);
						this.updateCurrentIndex();
						this.showTabBar = this.shouldShowTabBar() && this.show;

						console.log('✓ 使用 menu.json 中的默认菜单配置成功');
					} else {
						console.warn('menu.json 中没有 isShow=true 的菜单项，使用简单配置');
						this.initSimpleTabBar();
					}
				} catch (error) {
					console.error('使用默认 TabBar 配置失败:', error);
					// 使用最简单的配置
					this.initSimpleTabBar();
				}
			},

			/**
			 * 初始化简单 TabBar 配置（备用方案）
			 */
			initSimpleTabBar() {
				console.log('初始化简单 TabBar 配置（从 menu.json 获取）');

				try {
					// 尝试从 menu.json 中获取 isShow 为 true 的菜单项
					const originalMenus = menuMatcher.localMenuList || [];
					const defaultVisibleMenus = originalMenus.filter(item => item.isShow === true);

					if (defaultVisibleMenus.length > 0) {
						// 转换为简单的 uView TabBar 格式
						this.tabBarList = defaultVisibleMenus.map(item => ({
							iconPath: item.iconPath,
							selectedIconPath: item.selectedIconPath,
							text: item.text
						}));

						console.log('✓ 使用 menu.json 中的简单配置:', this.tabBarList.length, '项');
					} else {
						// 如果 menu.json 中没有数据，使用最基本的配置
						console.warn('menu.json 中没有可用菜单，使用最基本配置');
						this.tabBarList = [
							{
								iconPath: '/static/image/zfgs/my.png',
								selectedIconPath: '/static/image/zfgs/my_select.png',
								text: '我的'
							}
						];
					}
				} catch (error) {
					console.error('从 menu.json 获取简单配置失败:', error);
					// 最后的备用方案
					this.tabBarList = [
						{
							iconPath: '/static/image/zfgs/my.png',
							selectedIconPath: '/static/image/zfgs/my_select.png',
							text: '我的'
						}
					];
				}

				this.showTabBar = true;
				console.log('简单 TabBar 配置完成:', this.tabBarList);
			},
			
			/**
			 * TabBar 切换事件
			 */
			onTabBarChange(index) {
				console.log('=== TabBar 切换事件 ===');
				console.log('点击索引:', index);
				console.log('当前计算索引:', this.currentTabBarIndex);
				console.log('TabBar 列表长度:', this.tabBarList.length);

				if (index >= 0 && index < this.tabBarList.length) {
					const item = this.tabBarList[index];
					console.log('切换到项目:', item.text, item.originalPagePath);

					// 直接触发事件，让父组件更新 currentIndex
					this.$emit('change', index, item);

					console.log('=== TabBar 切换事件结束 ===');
				}
			},

			/**
			 * 页面跳转
			 */
			navigateToPage(pagePath) {
				try {
					// 确保路径格式正确
					const url = pagePath.startsWith('/') ? pagePath : `/${pagePath}`;

					console.log('准备跳转到页面:', url);

					// 由于我们使用的是 TabBar 容器页面，不需要实际跳转
					// 只需要更新容器页面的显示内容即可
					console.log('TabBar 容器模式，无需页面跳转');

				} catch (error) {
					console.error('页面跳转异常:', error);
				}
			},

			/**
			 * 强制更新 TabBar 状态
			 */
			forceUpdate() {
				console.log('强制更新 TabBar 状态');
				this.$forceUpdate();
			},

			/**
			 * 手动设置当前索引（保留接口兼容性）
			 */
			setCurrentIndex(index) {
				console.log('手动设置当前索引:', index);
				// 通知父组件更新索引
				this.$emit('update:currentIndex', index);
			},
			
			/**
			 * 检查当前页面是否应该显示 TabBar
			 */
			shouldShowTabBar() {
				try {
					// 获取当前页面路径
					let currentRoute = '';
					
					if (typeof uni !== 'undefined' && typeof uni.getCurrentPages === 'function') {
						const pages = uni.getCurrentPages();
						if (pages && pages.length > 0) {
							currentRoute = pages[pages.length - 1].route;
						}
					}
					
					// 定义不应该显示 TabBar 的页面
					const nonTabBarPages = [
						'pages/sys/index/login/index',
						'pages/debug/tabbar',
						'pages/login/login',
						'pages/register/register',
						'pages/common/webview'
					];
					
					// 检查是否为不应该显示 TabBar 的页面
					const shouldNotShow = nonTabBarPages.some(pagePath => {
						const normalizedPagePath = pagePath.startsWith('/') ? pagePath.substring(1) : pagePath;
						return normalizedPagePath === currentRoute || pagePath === `/${currentRoute}`;
					});
					
					const shouldShow = !shouldNotShow;
					console.log(`页面 ${currentRoute} 是否应该显示 TabBar:`, shouldShow);
					
					return shouldShow;
				} catch (error) {
					console.error('检查页面显示 TabBar 失败:', error);
					return true; // 默认显示
				}
			},
			
			/**
			 * 更新当前索引
			 */
			updateCurrentIndex() {
				console.log('updateCurrentIndex 被调用，当前使用计算属性管理索引');
				// 现在使用计算属性 currentTabBarIndex 来管理索引，这个方法保留用于兼容性
			},
			
			/**
			 * 构建动态 TabBar 配置
			 */
			async buildDynamicTabBarConfig(apiMenuItems) {
				console.log('开始构建动态 TabBar 配置...');
				console.log('API 菜单项数量:', apiMenuItems?.length || 0);
				console.log('API 菜单项详情:', apiMenuItems);

				const result = [];

				// 处理 API 菜单项
				if (apiMenuItems && apiMenuItems.length > 0) {
					apiMenuItems.forEach((apiItem, index) => {
						const menuName = apiItem.menuName || apiItem.text || '未知菜单';
						const extendS2 = apiItem.extend?.extendS2 || '';
						console.log(`处理 API 菜单项 ${index + 1}: ${menuName} ${extendS2}`);

						// 在配置池中查找匹配的配置
						const matchedConfig = this.defaultTabBarPool.find(poolItem => {
							const isMatch = poolItem.extend.extendS2 === extendS2;
							console.log(`  检查配置池项 "${poolItem.text}": ${poolItem.extend.extendS2} === ${extendS2} = ${isMatch}`);
							return isMatch;
						});

						if (matchedConfig) {
							console.log(`  ✓ 找到匹配配置: ${matchedConfig.text}`);

							// 创建 TabBar 项目，优先使用 API 数据，配置池数据作为补充
							const tabBarItem = {
								text: menuName || matchedConfig.text,
								pagePath: this.normalizePagePath(apiItem.menuUrl || apiItem.pagePath || matchedConfig.pagePath),
								iconPath: matchedConfig.iconPath,
								selectedIconPath: matchedConfig.selectedIconPath,
								extend: apiItem.extend || matchedConfig.extend
							};

							console.log(`  添加到结果: ${tabBarItem.text} (${tabBarItem.pagePath})`);
							result.push(tabBarItem);
						} else {
							console.log(`  ✗ 未找到匹配配置，跳过: ${menuName} (${extendS2})`);
						}
					});
				}

				// 检查是否已经包含"我的"页面，如果没有则添加
				const hasMyPage = result.some(item =>
					item.pagePath.includes('pages/sys/index/user/index') ||
					item.extend?.extendS2 === 'menu:user:my'
				);

				if (!hasMyPage) {
					console.log('添加固定的"我的" TabBar 到最后位置');
					result.push(this.myTabBarConfig);
				} else {
					console.log('"我的" TabBar 已存在，无需重复添加');
				}

				// 注释掉自动补充菜单的逻辑，严格按照API返回的数据显示
				// 如果API没有返回数据，只显示"我的"菜单
				console.log('TabBar 配置构建完成，不自动补充额外菜单');
				console.log('当前结果数量:', result.length);

				// 如果API没有返回任何数据，确保至少有"我的"菜单
				if (result.length === 0) {
					console.log('API 未返回任何菜单数据，仅显示"我的"菜单');
					result.push(this.myTabBarConfig);
				}

				console.log('动态 TabBar 配置构建完成，总数量:', result.length);
				result.forEach((item, index) => {
					console.log(`  ${index}: ${item.text} (${item.pagePath})`);
				});

				return result;
			},

			/**
			 * 标准化页面路径
			 */
			normalizePagePath(pagePath) {
				if (!pagePath) return '';

				// 移除开头的斜杠
				let normalized = pagePath.startsWith('/') ? pagePath.substring(1) : pagePath;

				// 确保路径格式正确
				if (!normalized.startsWith('pages/')) {
					normalized = `pages/${normalized}`;
				}

				return normalized;
			},

			/**
			 * 监听页面变化
			 */
			listenPageChange() {
				// 监听页面显示事件
				uni.$on('pageShow', () => {
					console.log('页面显示事件');
					setTimeout(() => {
						this.updateCurrentIndex();
						this.showTabBar = this.shouldShowTabBar() && this.show;
					}, 100);
				});

				// 监听 TabBar 更新事件
				uni.$on('updateTabBar', (tabBarList) => {
					console.log('收到 TabBar 更新事件:', tabBarList);
					this.updateTabBarFromVuex(tabBarList);
				});
			}
		},
		
		beforeDestroy() {
			// 清理事件监听
			uni.$off('pageShow');
			uni.$off('updateTabBar');
		}
	};
</script>

<style scoped>
	/* 可以在这里添加自定义样式覆盖 uView 默认样式 */
</style>
